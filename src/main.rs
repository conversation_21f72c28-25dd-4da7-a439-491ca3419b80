mod core;

use core::math::Vector2;

fn main() {
    println!("🎮 Verturion Game Engine - Vector2 Implementation Test");
    println!("======================================================");

    // Test basic Vector2 functionality
    let v1 = Vector2::new(3.0, 4.0);
    let v2 = Vector2::RIGHT;

    println!("\n📐 Basic Vector Operations:");
    println!("Vector v1: {}", v1);
    println!("Vector v2: {}", v2);
    println!("Length of v1: {:.6}", v1.length());
    println!("Normalized v1: {}", v1.normalized());
    println!("Dot product: {}", v1.dot(v2));
    println!("Cross product: {}", v1.cross(v2));

    // Test new mathematical operations
    println!("\n🔢 Mathematical Operations:");
    println!("Angle of v1: {:.4} radians ({:.1}°)", v1.angle(), v1.angle().to_degrees());
    println!("Angle between v1 and v2: {:.4} radians ({:.1}°)", v1.angle_to(v2), v1.angle_to(v2).to_degrees());

    let rotated = v1.rotated(std::f32::consts::FRAC_PI_4); // 45 degrees
    println!("v1 rotated 45°: ({:.3}, {:.3})", rotated.x, rotated.y);

    let orthogonal = v1.orthogonal();
    println!("Orthogonal to v1: {}", orthogonal);

    let projected = v1.project(v2);
    println!("v1 projected onto v2: {}", projected);

    let reflected = Vector2::new(1.0, -1.0).reflect(Vector2::new(0.0, 1.0));
    println!("(1, -1) reflected off horizontal surface: {}", reflected);

    // Test interpolation methods
    println!("\n🔄 Interpolation Methods:");
    let from = Vector2::new(0.0, 0.0);
    let to = Vector2::new(10.0, 10.0);

    let lerped = from.lerp(to, 0.5);
    println!("Linear interpolation (50%): {}", lerped);

    let moved = from.move_toward(to, 3.0);
    println!("Move toward target (max 3.0): {}", moved);

    // Test utility functions
    println!("\n🛠️ Utility Functions:");
    let negative = Vector2::new(-3.0, -4.0);
    println!("Absolute value of {}: {}", negative, negative.abs());

    let clamped = Vector2::new(-5.0, 15.0).clamp(Vector2::new(-2.0, 0.0), Vector2::new(10.0, 10.0));
    println!("Clamped vector: {}", clamped);

    let snapped = Vector2::new(3.7, 8.2).snapped(Vector2::new(2.0, 5.0));
    println!("Snapped to grid: {}", snapped);

    // Test boolean checks
    println!("\n✅ Boolean Checks:");
    let unit = Vector2::new(1.0, 0.0);
    println!("Is {} normalized? {}", unit, unit.is_normalized());
    println!("Is {} finite? {}", unit, unit.is_finite());
    println!("Is {} approximately zero? {}", Vector2::ZERO, Vector2::ZERO.is_zero_approx());

    let almost_equal1 = Vector2::new(1.0, 2.0);
    let almost_equal2 = Vector2::new(1.0000001, 2.0000001);
    println!("Are {} and {} approximately equal? {}", almost_equal1, almost_equal2, almost_equal1.is_equal_approx(almost_equal2));

    // Test fast inverse square root performance alternative
    println!("\n⚡ Fast Inverse Square Root Performance:");
    let test_value: f32 = 16.0;
    let standard_result = 1.0 / test_value.sqrt();
    let fast_result = Vector2::fast_inv_sqrt_fast(test_value);
    println!("Standard library 1/√{}: {:.6}", test_value, standard_result);
    println!("Fast dual constants 1/√{}: {:.6}", test_value, fast_result);
    println!("Error: {:.6}% (within acceptable range for performance-critical code)",
             ((fast_result - standard_result) / standard_result * 100.0).abs());
}
