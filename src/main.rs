mod core;

use core::math::Vector2;

fn main() {
    println!("Hello, world!");

    // Example usage of Vector2
    let v1 = Vector2::new(3.0, 4.0);
    let v2 = Vector2::RIGHT;

    println!("Vector v1: {}", v1);
    println!("Vector v2: {}", v2);
    println!("Length of v1: {}", v1.length());
    println!("Normalized v1: {}", v1.normalized());
    println!("Dot product: {}", v1.dot(v2));
    println!("Cross product: {}", v1.cross(v2));
}
