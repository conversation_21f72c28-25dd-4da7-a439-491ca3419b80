mod core;

use core::math::{Vector2, Vector2i, Vector3, Vector3i, Vector4, Vector4i};

fn main() {
    println!("🎮 Verturion Game Engine - Vector2 Implementation Test");
    println!("======================================================");

    // Test basic Vector2 functionality
    let v1 = Vector2::new(3.0, 4.0);
    let v2 = Vector2::RIGHT;

    println!("\n📐 Basic Vector Operations:");
    println!("Vector v1: {}", v1);
    println!("Vector v2: {}", v2);
    println!("Length of v1: {:.6}", v1.length());
    println!("Normalized v1: {}", v1.normalized());
    println!("Dot product: {}", v1.dot(v2));
    println!("Cross product: {}", v1.cross(v2));

    // Test new mathematical operations
    println!("\n🔢 Mathematical Operations:");
    println!("Angle of v1: {:.4} radians ({:.1}°)", v1.angle(), v1.angle().to_degrees());
    println!("Angle between v1 and v2: {:.4} radians ({:.1}°)", v1.angle_to(v2), v1.angle_to(v2).to_degrees());

    let rotated = v1.rotated(std::f32::consts::FRAC_PI_4); // 45 degrees
    println!("v1 rotated 45°: ({:.3}, {:.3})", rotated.x, rotated.y);

    let orthogonal = v1.orthogonal();
    println!("Orthogonal to v1: {}", orthogonal);

    let projected = v1.project(v2);
    println!("v1 projected onto v2: {}", projected);

    let reflected = Vector2::new(1.0, -1.0).reflect(Vector2::new(0.0, 1.0));
    println!("(1, -1) reflected off horizontal surface: {}", reflected);

    // Test interpolation methods
    println!("\n🔄 Interpolation Methods:");
    let from = Vector2::new(0.0, 0.0);
    let to = Vector2::new(10.0, 10.0);

    let lerped = from.lerp(to, 0.5);
    println!("Linear interpolation (50%): {}", lerped);

    let moved = from.move_toward(to, 3.0);
    println!("Move toward target (max 3.0): {}", moved);

    // Test utility functions
    println!("\n🛠️ Utility Functions:");
    let negative = Vector2::new(-3.0, -4.0);
    println!("Absolute value of {}: {}", negative, negative.abs());

    let clamped = Vector2::new(-5.0, 15.0).clamp(Vector2::new(-2.0, 0.0), Vector2::new(10.0, 10.0));
    println!("Clamped vector: {}", clamped);

    let snapped = Vector2::new(3.7, 8.2).snapped(Vector2::new(2.0, 5.0));
    println!("Snapped to grid: {}", snapped);

    // Test boolean checks
    println!("\n✅ Boolean Checks:");
    let unit = Vector2::new(1.0, 0.0);
    println!("Is {} normalized? {}", unit, unit.is_normalized());
    println!("Is {} finite? {}", unit, unit.is_finite());
    println!("Is {} approximately zero? {}", Vector2::ZERO, Vector2::ZERO.is_zero_approx());

    let almost_equal1 = Vector2::new(1.0, 2.0);
    let almost_equal2 = Vector2::new(1.0000001, 2.0000001);
    println!("Are {} and {} approximately equal? {}", almost_equal1, almost_equal2, almost_equal1.is_equal_approx(almost_equal2));

    // Test fast inverse square root performance alternative
    println!("\n⚡ Fast Inverse Square Root Performance:");
    let test_value: f32 = 16.0;
    let standard_result = 1.0 / test_value.sqrt();
    let fast_result = Vector2::fast_inv_sqrt_fast(test_value);
    println!("Standard library 1/√{}: {:.6}", test_value, standard_result);
    println!("Fast dual constants 1/√{}: {:.6}", test_value, fast_result);
    println!("Error: {:.6}% (within acceptable range for performance-critical code)",
             ((fast_result - standard_result) / standard_result * 100.0).abs());

    // Test Vector2i functionality
    println!("\n🔢 Vector2i (Integer Vector) Implementation Test");
    println!("================================================");

    // Test basic Vector2i operations
    let tile_pos = Vector2i::new(5, 3);
    let offset = Vector2i::new(-2, 1);

    println!("\n📐 Basic Integer Vector Operations:");
    println!("Tile position: {}", tile_pos);
    println!("Offset: {}", offset);
    println!("Length squared: {}", tile_pos.length_squared());
    println!("Dot product: {}", tile_pos.dot(offset));
    println!("Cross product: {}", tile_pos.cross(offset));

    // Test mathematical operations
    println!("\n🔢 Mathematical Operations:");
    let negative_pos = Vector2i::new(-3, -4);
    println!("Absolute value of {}: {}", negative_pos, negative_pos.abs());
    println!("Sign of {}: {}", negative_pos, negative_pos.sign());

    let pos1 = Vector2i::new(1, 5);
    let pos2 = Vector2i::new(3, 2);
    println!("Min of {} and {}: {}", pos1, pos2, pos1.min(pos2));
    println!("Max of {} and {}: {}", pos1, pos2, pos1.max(pos2));

    let player_pos = Vector2i::new(25, -3);
    let world_min = Vector2i::new(0, 0);
    let world_max = Vector2i::new(20, 20);
    let clamped_pos = player_pos.clamp(world_min, world_max);
    println!("Player position {} clamped to world bounds: {}", player_pos, clamped_pos);

    // Test utility functions
    println!("\n🛠️ Utility Functions:");
    let pixel_pos = Vector2i::new(123, 67);
    let grid_step = Vector2i::new(32, 32);
    let snapped_pos = pixel_pos.snapped(grid_step);
    println!("Pixel position {} snapped to {}x{} grid: {}", pixel_pos, grid_step.x, grid_step.y, snapped_pos);

    // Test distance calculations
    println!("\n📏 Distance Calculations:");
    let start = Vector2i::new(2, 3);
    let goal = Vector2i::new(5, 7);
    let manhattan_dist = start.manhattan_distance(goal);
    let chebyshev_dist = start.chebyshev_distance(goal);
    println!("From {} to {}:", start, goal);
    println!("  Manhattan distance: {} (grid moves)", manhattan_dist);
    println!("  Chebyshev distance: {} (diagonal moves)", chebyshev_dist);

    // Test boolean checks
    println!("\n✅ Boolean Checks:");
    println!("Is {} equal to {}? {}", tile_pos, Vector2i::new(5, 3), tile_pos.is_equal_approx(Vector2i::new(5, 3)));
    println!("Is {} zero? {}", Vector2i::ZERO, Vector2i::ZERO.is_zero_approx());
    println!("Is {} zero? {}", tile_pos, tile_pos.is_zero_approx());

    // Test conversion to Vector2
    println!("\n🔄 Vector2i to Vector2 Conversion:");
    let int_vec = Vector2i::new(3, 4);
    let float_vec = int_vec.to_vector2();
    println!("Integer vector: {}", int_vec);
    println!("Converted to float: {}", float_vec);
    println!("Float vector length: {:.6}", float_vec.length());
    println!("Float vector normalized: {}", float_vec.normalized());

    // Test Vector3 functionality
    println!("\n🌐 Vector3 (3D Floating-Point Vector) Implementation Test");
    println!("========================================================");

    // Test basic Vector3 operations
    let pos_3d = Vector3::new(3.0, 4.0, 5.0);
    let dir_3d = Vector3::new(1.0, 0.0, 0.0);

    println!("\n📐 Basic 3D Vector Operations:");
    println!("3D Position: {}", pos_3d);
    println!("3D Direction: {}", dir_3d);
    println!("Length: {:.6}", pos_3d.length());
    println!("Length squared: {:.6}", pos_3d.length_squared());
    println!("Normalized: {}", pos_3d.normalized());
    println!("Dot product: {:.6}", pos_3d.dot(dir_3d));

    let cross_result = pos_3d.cross(dir_3d);
    println!("Cross product: {}", cross_result);

    // Test 3D mathematical operations
    println!("\n🔢 3D Mathematical Operations:");
    let from_3d = Vector3::new(0.0, 0.0, 0.0);
    let to_3d = Vector3::new(6.0, 8.0, 10.0);
    let lerped_3d = from_3d.lerp(to_3d, 0.5);
    println!("3D Linear interpolation (50%): {}", lerped_3d);

    let distance_3d = from_3d.distance_to(to_3d);
    println!("Distance between points: {:.6}", distance_3d);

    let abs_3d = Vector3::new(-1.0, -2.0, -3.0).abs();
    println!("Absolute value: {}", abs_3d);

    // Test Vector3i functionality
    println!("\n🧊 Vector3i (3D Integer Vector) Implementation Test");
    println!("==================================================");

    // Test basic Vector3i operations
    let voxel_pos = Vector3i::new(5, 3, 7);
    let chunk_offset = Vector3i::new(-2, 1, -1);

    println!("\n📐 Basic 3D Integer Vector Operations:");
    println!("Voxel position: {}", voxel_pos);
    println!("Chunk offset: {}", chunk_offset);
    println!("Length squared: {}", voxel_pos.length_squared());
    println!("Dot product: {}", voxel_pos.dot(chunk_offset));

    let cross_3di = voxel_pos.cross(chunk_offset);
    println!("Cross product: {}", cross_3di);

    // Test 3D distance calculations
    println!("\n📏 3D Distance Calculations:");
    let start_3d = Vector3i::new(2, 3, 1);
    let goal_3d = Vector3i::new(5, 7, 4);
    let manhattan_3d = start_3d.manhattan_distance(goal_3d);
    let chebyshev_3d = start_3d.chebyshev_distance(goal_3d);
    println!("From {} to {}:", start_3d, goal_3d);
    println!("  Manhattan distance: {} (3D grid moves)", manhattan_3d);
    println!("  Chebyshev distance: {} (3D diagonal moves)", chebyshev_3d);

    // Test 3D conversion
    println!("\n🔄 Vector3i to Vector3 Conversion:");
    let int_3d = Vector3i::new(3, 4, 5);
    let float_3d = int_3d.to_vector3();
    println!("Integer 3D vector: {}", int_3d);
    println!("Converted to float: {}", float_3d);
    println!("Float 3D vector length: {:.6}", float_3d.length());

    // Test Vector4 functionality
    println!("\n🌌 Vector4 (4D Floating-Point Vector) Implementation Test");
    println!("========================================================");

    // Test basic Vector4 operations
    let quaternion = Vector4::new(0.0, 0.0, 0.0, 1.0);
    let color_rgba = Vector4::new(1.0, 0.5, 0.25, 1.0);

    println!("\n📐 Basic 4D Vector Operations:");
    println!("Quaternion (identity): {}", quaternion);
    println!("RGBA Color: {}", color_rgba);
    println!("Color length: {:.6}", color_rgba.length());
    println!("Color normalized: {}", color_rgba.normalized());
    println!("Dot product: {:.6}", quaternion.dot(color_rgba));

    // Test 4D interpolation
    println!("\n🔢 4D Mathematical Operations:");
    let from_4d = Vector4::new(0.0, 0.0, 0.0, 0.0);
    let to_4d = Vector4::new(8.0, 6.0, 4.0, 2.0);
    let lerped_4d = from_4d.lerp(to_4d, 0.5);
    println!("4D Linear interpolation (50%): {}", lerped_4d);

    let abs_4d = Vector4::new(-1.0, -2.0, -3.0, -4.0).abs();
    println!("Absolute value: {}", abs_4d);

    // Test Vector4i functionality
    println!("\n🎨 Vector4i (4D Integer Vector) Implementation Test");
    println!("==================================================");

    // Test basic Vector4i operations
    let color_int = Vector4i::new(255, 128, 64, 255);
    let grid_4d = Vector4i::new(10, 20, 30, 40);

    println!("\n📐 Basic 4D Integer Vector Operations:");
    println!("Integer RGBA Color: {}", color_int);
    println!("4D Grid position: {}", grid_4d);
    println!("Grid length squared: {}", grid_4d.length_squared());
    println!("Dot product: {}", color_int.dot(grid_4d));

    // Test 4D color operations
    println!("\n🎨 4D Color Operations:");
    let invalid_color = Vector4i::new(300, -10, 128, 500);
    let valid_color = invalid_color.clamp(Vector4i::ZERO, Vector4i::new(255, 255, 255, 255));
    println!("Invalid color: {}", invalid_color);
    println!("Clamped to valid range: {}", valid_color);

    let color1 = Vector4i::new(255, 100, 50, 255);
    let color2 = Vector4i::new(128, 200, 75, 128);
    let max_color = color1.max(color2);
    println!("Max of {} and {}: {}", color1, color2, max_color);

    // Test 4D conversion
    println!("\n🔄 Vector4i to Vector4 Conversion:");
    let int_4d = Vector4i::new(255, 128, 64, 255);
    let float_4d = int_4d.to_vector4();
    println!("Integer 4D vector: {}", int_4d);
    println!("Converted to float: {}", float_4d);
    println!("Float 4D vector length: {:.6}", float_4d.length());
}
