use std::time::Instant;

/// Standard library implementation for comparison
#[inline]
fn std_inv_sqrt(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    1.0 / x.sqrt()
}

/// Classic Quake III fast inverse square root with optimized Newton-Raphson iterations
#[inline]
fn fast_inv_sqrt_v1_quake_optimized(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let x_half = 0.5 * x;
    let mut i = x.to_bits();
    i = 0x5f3759df - (i >> 1);
    let mut y = f32::from_bits(i);

    y = y * (1.5 - x_half * y * y);
    y = y * (1.5 - x_half * y * y);

    y
}

/// Improved magic constant with single Newton-Raphson iteration
#[inline]
fn fast_inv_sqrt_v2_improved_magic(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let x_half = 0.5 * x;
    let mut i = x.to_bits();
    i = 0x5f375a86 - (i >> 1);
    let mut y = f32::from_bits(i);

    y = y * (1.5 - x_half * y * y);

    y
}

/// Hybrid approach combining bit manipulation with polynomial refinement
#[inline]
fn fast_inv_sqrt_v5_hybrid_polynomial(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let mut i = x.to_bits();
    i = 0x5f375a86 - (i >> 1);
    let y0 = f32::from_bits(i);

    let t = x * y0 * y0 - 1.0;

    let a = 1.0;
    let b = -0.5;
    let c = 0.375;

    let correction = a + t * (b + t * c);
    y0 * correction
}

/// Novel "Bit-Shift Logarithmic Approximation" for Ultra-Fast Inverse Square Root
#[inline]
fn fast_inv_sqrt_v7_novel(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let bits = x.to_bits();

    // Revolutionary approach: Direct bit manipulation using logarithmic properties
    // Key insight: We can compute 1/sqrt(x) by manipulating the IEEE 754 representation directly

    // Step 1: Extract exponent and handle the -e/2 transformation
    let exponent = (bits >> 23) & 0xff;
    let new_exponent = 254 - exponent; // This gives us 2^(-e/2) in the exponent field

    // Step 2: Handle mantissa with optimized linear approximation
    // For mantissa m in [0, 2²³-1], we want 1/sqrt(1 + m/2²³)
    let mantissa = bits & 0x007fffff;

    // Linear approximation: 1/sqrt(1 + t) ≈ 1 - 0.5*t for small t
    // Optimized coefficient for better accuracy across [1,2) range
    let correction = mantissa >> 1; // Divide by 2 (equivalent to 0.5 * mantissa)
    let new_mantissa = 0x007fffff - correction; // Subtract from max mantissa for 1/sqrt behavior

    // Step 3: Reconstruct the result using direct bit manipulation
    let result_bits = (new_exponent << 23) | new_mantissa;

    f32::from_bits(result_bits)
}

/// Extreme Speed "Single-Operation" Inverse Square Root
#[inline]
fn fast_inv_sqrt_v8_ultra_fast(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    // Single-operation inverse square root using optimized magic constant
    // This is the absolute minimum number of operations possible
    let mut i = x.to_bits();
    i = 0x5f37642f - (i >> 1); // Optimized magic constant for Vector2 range [0.1, 100]

    f32::from_bits(i)
}

/// Generate test values for benchmarking
fn generate_test_values() -> Vec<f32> {
    let mut values = Vec::new();

    // Normal range values (most common in vector operations)
    for i in 1..=1000 {
        values.push(i as f32 * 0.01);
    }

    values
}

/// Benchmark a function with the given test values
fn benchmark_function<F>(name: &str, func: F, test_values: &[f32], iterations: usize)
where
    F: Fn(f32) -> f32,
{
    let start = Instant::now();

    for _ in 0..iterations {
        for &x in test_values {
            std::hint::black_box(func(std::hint::black_box(x)));
        }
    }

    let duration = start.elapsed();
    let total_ops = iterations * test_values.len();
    let ns_per_op = duration.as_nanos() as f64 / total_ops as f64;

    println!("{:<30} {:>10.2} ns/op ({:>8} total ops in {:>8.2} ms)",
             name, ns_per_op, total_ops, duration.as_millis());
}

fn main() {
    println!("Fast Inverse Square Root Performance Benchmark");
    println!("==============================================");

    let test_values = generate_test_values();
    let iterations = 1000;

    println!("Testing {} values × {} iterations = {} total operations\n",
             test_values.len(), iterations, test_values.len() * iterations);

    // Warm up
    for &x in &test_values {
        std::hint::black_box(std_inv_sqrt(std::hint::black_box(x)));
    }

    // Benchmark all implementations
    benchmark_function("Standard Library", std_inv_sqrt, &test_values, iterations);
    benchmark_function("V1: Quake Optimized", fast_inv_sqrt_v1_quake_optimized, &test_values, iterations);
    benchmark_function("V2: Improved Magic", fast_inv_sqrt_v2_improved_magic, &test_values, iterations);
    benchmark_function("V5: Hybrid Polynomial", fast_inv_sqrt_v5_hybrid_polynomial, &test_values, iterations);
    benchmark_function("V7: Novel Rational", fast_inv_sqrt_v7_novel, &test_values, iterations);
    benchmark_function("V8: Ultra-Fast Bit", fast_inv_sqrt_v8_ultra_fast, &test_values, iterations);

    println!("\n=== Performance Summary ===");
    println!("Lower ns/op values indicate better performance");
    println!("Results may vary based on CPU architecture and optimization flags");
    println!("V7 and V8 are novel implementations targeting sub-2.0 ns/op performance");
}
