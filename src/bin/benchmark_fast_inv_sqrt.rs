use std::time::Instant;

/// Standard library implementation for comparison
#[inline]
fn std_inv_sqrt(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    1.0 / x.sqrt()
}

/// Classic Quake III fast inverse square root with optimized Newton-Raphson iterations
#[inline]
fn fast_inv_sqrt_v1_quake_optimized(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let x_half = 0.5 * x;
    let mut i = x.to_bits();
    i = 0x5f3759df - (i >> 1);
    let mut y = f32::from_bits(i);

    y = y * (1.5 - x_half * y * y);
    y = y * (1.5 - x_half * y * y);

    y
}

/// Improved magic constant with single Newton-Raphson iteration
#[inline]
fn fast_inv_sqrt_v2_improved_magic(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let x_half = 0.5 * x;
    let mut i = x.to_bits();
    i = 0x5f375a86 - (i >> 1);
    let mut y = f32::from_bits(i);

    y = y * (1.5 - x_half * y * y);

    y
}

/// Hybrid approach combining bit manipulation with polynomial refinement
#[inline]
fn fast_inv_sqrt_v5_hybrid_polynomial(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let mut i = x.to_bits();
    i = 0x5f375a86 - (i >> 1);
    let y0 = f32::from_bits(i);
    
    let t = x * y0 * y0 - 1.0;
    
    let a = 1.0;
    let b = -0.5;
    let c = 0.375;
    
    let correction = a + t * (b + t * c);
    y0 * correction
}

/// Generate test values for benchmarking
fn generate_test_values() -> Vec<f32> {
    let mut values = Vec::new();
    
    // Normal range values (most common in vector operations)
    for i in 1..=1000 {
        values.push(i as f32 * 0.01);
    }
    
    values
}

/// Benchmark a function with the given test values
fn benchmark_function<F>(name: &str, func: F, test_values: &[f32], iterations: usize)
where
    F: Fn(f32) -> f32,
{
    let start = Instant::now();
    
    for _ in 0..iterations {
        for &x in test_values {
            std::hint::black_box(func(std::hint::black_box(x)));
        }
    }
    
    let duration = start.elapsed();
    let total_ops = iterations * test_values.len();
    let ns_per_op = duration.as_nanos() as f64 / total_ops as f64;
    
    println!("{:<30} {:>10.2} ns/op ({:>8} total ops in {:>8.2} ms)", 
             name, ns_per_op, total_ops, duration.as_millis());
}

fn main() {
    println!("Fast Inverse Square Root Performance Benchmark");
    println!("==============================================");
    
    let test_values = generate_test_values();
    let iterations = 1000;
    
    println!("Testing {} values × {} iterations = {} total operations\n", 
             test_values.len(), iterations, test_values.len() * iterations);
    
    // Warm up
    for &x in &test_values {
        std::hint::black_box(std_inv_sqrt(std::hint::black_box(x)));
    }
    
    // Benchmark all implementations
    benchmark_function("Standard Library", std_inv_sqrt, &test_values, iterations);
    benchmark_function("V1: Quake Optimized", fast_inv_sqrt_v1_quake_optimized, &test_values, iterations);
    benchmark_function("V2: Improved Magic", fast_inv_sqrt_v2_improved_magic, &test_values, iterations);
    benchmark_function("V5: Hybrid Polynomial", fast_inv_sqrt_v5_hybrid_polynomial, &test_values, iterations);
    
    println!("\n=== Performance Summary ===");
    println!("Lower ns/op values indicate better performance");
    println!("Results may vary based on CPU architecture and optimization flags");
}
