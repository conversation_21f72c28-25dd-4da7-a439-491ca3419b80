use std::time::Instant;

/// Brute-Force Magic Constant Optimization for V9 Fast Inverse Square Root
///
/// This program systematically searches for the optimal magic constant that minimizes
/// error in the V9 single-operation fast inverse square root implementation.
///
/// Search Range: 0x5f370000 to 0x5f380000 (65,536 possible values)
/// Formula: f32::from_bits(magic_constant - (x.to_bits() >> 1))
/// Target: Minimize maximum relative error across Vector2 range [0.1, 100]

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
struct MagicConstantResult {
    magic_constant: u32,
    max_error: f64,
    avg_error: f64,
    worst_case_input: f32,
}

impl MagicConstantResult {
    fn new(magic_constant: u32) -> Self {
        Self {
            magic_constant,
            max_error: f64::INFINITY,
            avg_error: f64::INFINITY,
            worst_case_input: 0.0,
        }
    }
}

/// Fast inverse square root using specified magic constant
#[inline]
fn fast_inv_sqrt_with_magic(x: f32, magic_constant: u32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    // V9 single-operation formula with custom magic constant
    f32::from_bits(magic_constant - (x.to_bits() >> 1))
}

/// Calculate accuracy metrics for a given magic constant
fn calculate_accuracy_for_magic(magic_constant: u32, test_values: &[f32]) -> MagicConstantResult {
    let mut max_error = 0.0f64;
    let mut total_error = 0.0f64;
    let mut worst_case_input = 0.0f32;
    let mut valid_count = 0;

    for &x in test_values {
        let fast_result = fast_inv_sqrt_with_magic(x, magic_constant);
        let accurate_result = 1.0 / x.sqrt();

        // Skip invalid results
        if fast_result.is_nan() || fast_result.is_infinite() || accurate_result.is_nan() || accurate_result.is_infinite() {
            continue;
        }

        // Calculate relative error
        let relative_error = (((fast_result - accurate_result) / accurate_result).abs() * 100.0) as f64;

        if relative_error > max_error {
            max_error = relative_error;
            worst_case_input = x;
        }

        total_error += relative_error;
        valid_count += 1;
    }

    let avg_error = if valid_count > 0 { total_error / valid_count as f64 } else { f64::INFINITY };

    MagicConstantResult {
        magic_constant,
        max_error,
        avg_error,
        worst_case_input,
    }
}

/// Generate test values for Vector2 optimization range [0.1, 100]
fn generate_test_values() -> Vec<f32> {
    let mut values = Vec::new();

    // Linear distribution in [0.1, 100] range
    for i in 0..300 {
        let t = i as f32 / 299.0;
        let value = 0.1 + t * (100.0 - 0.1);
        values.push(value);
    }

    // Logarithmic distribution for better coverage
    for i in 0..300 {
        let t = i as f32 / 299.0;
        let log_min = 0.1f32.ln();
        let log_max = 100.0f32.ln();
        let value = (log_min + t * (log_max - log_min)).exp();
        values.push(value);
    }

    // Powers of 2 and their reciprocals (common in vector operations)
    for i in -10..=10 {
        let value = 2.0f32.powi(i);
        if value >= 0.1 && value <= 100.0 {
            values.push(value);
        }
    }

    // Common vector magnitudes
    let common_values = [
        0.1, 0.2, 0.5, 0.7071067811865476, // sqrt(0.5)
        1.0, 1.4142135623730951, // sqrt(2)
        2.0, 3.0, 5.0, 10.0, 20.0, 50.0, 100.0
    ];

    for &value in &common_values {
        if value >= 0.1 && value <= 100.0 {
            values.push(value);
        }
    }

    // Random values for comprehensive coverage
    use std::collections::HashSet;
    let mut rng_state = 12345u32; // Simple LCG for reproducible results
    let mut seen = HashSet::new();

    while values.len() < 900 {
        // Linear congruential generator
        rng_state = rng_state.wrapping_mul(1664525).wrapping_add(1013904223);
        let random_f32 = (rng_state as f32) / (u32::MAX as f32);
        let value = 0.1 + random_f32 * (100.0 - 0.1);

        // Avoid duplicates
        let key = (value * 1000000.0) as u32;
        if seen.insert(key) {
            values.push(value);
        }
    }

    values.truncate(900);
    values.sort_by(|a, b| a.partial_cmp(b).unwrap());
    values
}

fn main() {
    println!("🔍 Brute-Force Magic Constant Optimization for V9 Fast Inverse Square Root");
    println!("================================================================================");
    println!("Search Range: 0x5f370000 to 0x5f380000 (65,536 possible values)");
    println!("Formula: f32::from_bits(magic_constant - (x.to_bits() >> 1))");
    println!("Target: Minimize maximum relative error across Vector2 range [0.1, 100]");
    println!("Test Values: 900 carefully selected values");
    println!();

    // Generate test values
    println!("📊 Generating test values...");
    let test_values = generate_test_values();
    println!("✅ Generated {} test values in range [0.1, 100]", test_values.len());
    println!();

    // Search parameters
    const SEARCH_START: u32 = 0x5f370000;
    const SEARCH_END: u32 = 0x5f380000;
    const TOTAL_CONSTANTS: u32 = SEARCH_END - SEARCH_START;
    const CURRENT_V9_MAGIC: u32 = 0x5f37642f;

    println!("🎯 Current V9 magic constant: 0x{:08x} ({})", CURRENT_V9_MAGIC, CURRENT_V9_MAGIC);

    // Test current V9 constant for baseline
    let v9_baseline = calculate_accuracy_for_magic(CURRENT_V9_MAGIC, &test_values);
    println!("📈 V9 Baseline - Max Error: {:.6}%, Avg Error: {:.6}%",
             v9_baseline.max_error, v9_baseline.avg_error);
    println!();

    // Initialize tracking variables
    let mut best_result = MagicConstantResult::new(CURRENT_V9_MAGIC);
    best_result.max_error = v9_baseline.max_error;
    best_result.avg_error = v9_baseline.avg_error;
    best_result.worst_case_input = v9_baseline.worst_case_input;

    let mut top_10_results: Vec<MagicConstantResult> = Vec::new();
    let start_time = Instant::now();
    let mut last_progress_time = start_time;

    println!("🚀 Starting brute-force search...");
    println!("Progress updates every 1000 tested constants");
    println!();

    // Main search loop
    for (index, magic_constant) in (SEARCH_START..SEARCH_END).enumerate() {
        let result = calculate_accuracy_for_magic(magic_constant, &test_values);

        // Update best result if we found a better one
        if result.max_error < best_result.max_error {
            best_result = result.clone();
            println!("🎉 NEW BEST! Magic: 0x{:08x}, Max Error: {:.6}%, Avg Error: {:.6}%",
                     magic_constant, result.max_error, result.avg_error);
        }

        // Maintain top 10 list
        top_10_results.push(result);
        top_10_results.sort_by(|a, b| a.max_error.partial_cmp(&b.max_error).unwrap());
        if top_10_results.len() > 10 {
            top_10_results.truncate(10);
        }

        // Progress updates every 1000 constants
        if (index + 1) % 1000 == 0 {
            let progress = ((index + 1) as f64 / TOTAL_CONSTANTS as f64) * 100.0;
            let _elapsed = start_time.elapsed();
            let current_time = Instant::now();
            let time_since_last = current_time.duration_since(last_progress_time);
            last_progress_time = current_time;

            println!("📊 Progress: {:.1}% ({}/{}) - {:.2}s for last 1000 - Best: {:.6}%",
                     progress, index + 1, TOTAL_CONSTANTS, time_since_last.as_secs_f64(), best_result.max_error);
        }
    }

    let total_time = start_time.elapsed();
    println!();
    println!("✅ Search completed in {:.2} seconds", total_time.as_secs_f64());
    println!();

    // Final results
    println!("🏆 OPTIMIZATION RESULTS");
    println!("================================================================================");
    println!();

    println!("📊 BASELINE (Current V9):");
    println!("   Magic Constant: 0x{:08x} ({})", CURRENT_V9_MAGIC, CURRENT_V9_MAGIC);
    println!("   Max Error: {:.6}%", v9_baseline.max_error);
    println!("   Avg Error: {:.6}%", v9_baseline.avg_error);
    println!("   Worst Case Input: {}", v9_baseline.worst_case_input);
    println!();

    println!("🎯 OPTIMAL RESULT:");
    println!("   Magic Constant: 0x{:08x} ({})", best_result.magic_constant, best_result.magic_constant);
    println!("   Max Error: {:.6}%", best_result.max_error);
    println!("   Avg Error: {:.6}%", best_result.avg_error);
    println!("   Worst Case Input: {}", best_result.worst_case_input);
    println!();

    let improvement = ((v9_baseline.max_error - best_result.max_error) / v9_baseline.max_error) * 100.0;
    if improvement > 0.0 {
        println!("📈 IMPROVEMENT: {:.4}% reduction in maximum error", improvement);
    } else {
        println!("📉 RESULT: No improvement found over current V9 constant");
    }
    println!();

    println!("🏅 TOP 10 MAGIC CONSTANTS:");
    println!("   Rank | Magic Constant | Max Error (%) | Avg Error (%) | Improvement");
    println!("   -----|----------------|---------------|---------------|------------");
    for (rank, result) in top_10_results.iter().enumerate() {
        let improvement = ((v9_baseline.max_error - result.max_error) / v9_baseline.max_error) * 100.0;
        println!("   {:4} | 0x{:08x}   | {:11.6} | {:11.6} | {:+9.4}%",
                 rank + 1, result.magic_constant, result.max_error, result.avg_error, improvement);
    }
    println!();

    if best_result.magic_constant != CURRENT_V9_MAGIC {
        println!("💡 RECOMMENDATION:");
        println!("   Consider updating V9 magic constant from 0x{:08x} to 0x{:08x}",
                 CURRENT_V9_MAGIC, best_result.magic_constant);
        println!("   This would reduce maximum error from {:.6}% to {:.6}%",
                 v9_baseline.max_error, best_result.max_error);
    } else {
        println!("✅ CONCLUSION:");
        println!("   Current V9 magic constant 0x{:08x} is already optimal!", CURRENT_V9_MAGIC);
        println!("   No better constant found in the search range.");
    }

    println!();
    println!("🔬 Search Statistics:");
    println!("   Constants Tested: {}", TOTAL_CONSTANTS);
    println!("   Test Values per Constant: {}", test_values.len());
    println!("   Total Calculations: {}", TOTAL_CONSTANTS as u64 * test_values.len() as u64);
    println!("   Search Time: {:.2} seconds", total_time.as_secs_f64());
    println!("   Rate: {:.0} constants/second", TOTAL_CONSTANTS as f64 / total_time.as_secs_f64());
}
