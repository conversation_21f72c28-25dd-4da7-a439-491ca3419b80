use std::time::Instant;
use std::collections::BinaryHeap;
use std::cmp::Ordering;

/// Simplified Dual Magic Constant Optimization
///
/// This program explores whether a simplified addition-only formula (result1 + result2)
/// can achieve comparable accuracy to the current weighted approach (0.7 * result1 + 0.3 * result2)
/// while significantly reducing computational overhead.
///
/// Target: Find magic constants that work optimally with simple addition
/// Performance Goal: Reduce from 3 operations (2 muls + 1 add) to 1 operation (1 add)

#[derive(Debug, Clone)]
struct SimplifiedResult {
    magic1: u32,
    magic2: u32,
    max_error: f64,
    avg_error: f64,
    worst_case_input: f32,
}

impl SimplifiedResult {
    fn new(magic1: u32, magic2: u32) -> Self {
        Self {
            magic1,
            magic2,
            max_error: f64::INFINITY,
            avg_error: f64::INFINITY,
            worst_case_input: 0.0,
        }
    }
}

// Implement ordering for BinaryHeap (min-heap based on max_error)
impl Ord for SimplifiedResult {
    fn cmp(&self, other: &Self) -> Ordering {
        other.max_error.partial_cmp(&self.max_error).unwrap_or(Ordering::Equal)
    }
}

impl PartialOrd for SimplifiedResult {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl PartialEq for SimplifiedResult {
    fn eq(&self, other: &Self) -> bool {
        self.max_error == other.max_error
    }
}

impl Eq for SimplifiedResult {}

/// Simplified dual magic constant implementation (addition only)
#[inline]
fn simplified_dual_inv_sqrt(x: f32, magic1: u32, magic2: u32) -> f32 {
    if x == 0.0 { return 0.0; }
    if x < 0.0 { return f32::NAN; }
    if x < 1e-10 { return 1.0 / x.sqrt(); }

    let bits = x.to_bits();
    let result1 = f32::from_bits(magic1 - (bits >> 1));
    let result2 = f32::from_bits(magic2 - (bits >> 1));

    // Simplified formula: just add the results (no weighting)
    (result1 + result2) * 0.5  // Average the results
}

/// Alternative simplified approach: subtraction-based
#[inline]
fn simplified_dual_inv_sqrt_sub(x: f32, magic1: u32, magic2: u32) -> f32 {
    if x == 0.0 { return 0.0; }
    if x < 0.0 { return f32::NAN; }
    if x < 1e-10 { return 1.0 / x.sqrt(); }

    let bits = x.to_bits();
    let result1 = f32::from_bits(magic1 - (bits >> 1));
    let result2 = f32::from_bits(magic2 - (bits >> 1));

    // Alternative: use difference for correction
    result1 + (result2 - result1) * 0.5
}

/// Current weighted dual magic constant implementation (for comparison)
#[inline]
fn weighted_dual_inv_sqrt(x: f32) -> f32 {
    if x == 0.0 { return 0.0; }
    if x < 0.0 { return f32::NAN; }
    if x < 1e-10 { return 1.0 / x.sqrt(); }

    let bits = x.to_bits();
    let result1 = f32::from_bits(0x5f371ef0 - (bits >> 1));
    let result2 = f32::from_bits(0x5f37f840 - (bits >> 1));
    0.7 * result1 + 0.3 * result2
}

/// Calculate accuracy metrics for simplified approach
fn calculate_simplified_accuracy(magic1: u32, magic2: u32, test_values: &[f32]) -> SimplifiedResult {
    let mut max_error = 0.0f64;
    let mut total_error = 0.0f64;
    let mut worst_case_input = 0.0f32;
    let mut valid_count = 0;

    for &x in test_values {
        let fast_result = simplified_dual_inv_sqrt(x, magic1, magic2);
        let accurate_result = 1.0 / x.sqrt();

        if fast_result.is_nan() || fast_result.is_infinite() ||
           accurate_result.is_nan() || accurate_result.is_infinite() {
            continue;
        }

        let relative_error = (((fast_result - accurate_result) / accurate_result).abs() * 100.0) as f64;

        if relative_error > max_error {
            max_error = relative_error;
            worst_case_input = x;
        }

        total_error += relative_error;
        valid_count += 1;
    }

    let avg_error = if valid_count > 0 { total_error / valid_count as f64 } else { f64::INFINITY };

    SimplifiedResult {
        magic1,
        magic2,
        max_error,
        avg_error,
        worst_case_input,
    }
}

/// Calculate accuracy for current weighted approach (baseline)
fn calculate_weighted_accuracy(test_values: &[f32]) -> (f64, f64, f32) {
    let mut max_error = 0.0f64;
    let mut total_error = 0.0f64;
    let mut worst_case_input = 0.0f32;
    let mut valid_count = 0;

    for &x in test_values {
        let fast_result = weighted_dual_inv_sqrt(x);
        let accurate_result = 1.0 / x.sqrt();

        if fast_result.is_nan() || fast_result.is_infinite() ||
           accurate_result.is_nan() || accurate_result.is_infinite() {
            continue;
        }

        let relative_error = (((fast_result - accurate_result) / accurate_result).abs() * 100.0) as f64;

        if relative_error > max_error {
            max_error = relative_error;
            worst_case_input = x;
        }

        total_error += relative_error;
        valid_count += 1;
    }

    let avg_error = if valid_count > 0 { total_error / valid_count as f64 } else { 0.0 };
    (max_error, avg_error, worst_case_input)
}

/// Generate test values for Vector2 optimization range [0.1, 100]
fn generate_test_values() -> Vec<f32> {
    let mut values = Vec::new();

    // Linear distribution
    for i in 0..300 {
        let t = i as f32 / 299.0;
        let value = 0.1 + t * (100.0 - 0.1);
        values.push(value);
    }

    // Logarithmic distribution
    for i in 0..300 {
        let t = i as f32 / 299.0;
        let log_min = 0.1f32.ln();
        let log_max = 100.0f32.ln();
        let value = (log_min + t * (log_max - log_min)).exp();
        values.push(value);
    }

    // Powers of 2 and critical values
    for i in -10..=10 {
        let value = 2.0f32.powi(i);
        if value >= 0.1 && value <= 100.0 {
            values.push(value);
        }
    }

    // Common vector magnitudes and edge cases
    let critical_values = [
        0.1, 0.2, 0.5, 0.7071067811865476, 1.0, 1.4142135623730951,
        2.0, 3.0, 5.0, 10.0, 20.0, 50.0, 100.0,
        0.15, 0.25, 0.33, 0.66, 1.5, 2.5, 7.5, 15.0, 25.0, 75.0
    ];

    for &value in &critical_values {
        if value >= 0.1 && value <= 100.0 {
            values.push(value);
        }
    }

    // Random values for comprehensive coverage
    use std::collections::HashSet;
    let mut rng_state = 12345u32;
    let mut seen = HashSet::new();

    while values.len() < 900 {
        rng_state = rng_state.wrapping_mul(1664525).wrapping_add(1013904223);
        let random_f32 = (rng_state as f32) / (u32::MAX as f32);
        let value = 0.1 + random_f32 * (100.0 - 0.1);

        let key = (value * 1000000.0) as u32;
        if seen.insert(key) {
            values.push(value);
        }
    }

    values.truncate(900);
    values.sort_by(|a, b| a.partial_cmp(b).unwrap());
    values
}

fn main() {
    println!("🔧 Simplified Dual Magic Constant Optimization");
    println!("===============================================");
    println!("Exploring addition-only formula: result1 + result2");
    println!("Goal: Reduce from 3 operations (2 muls + 1 add) to 1 operation (1 add)");
    println!("Target: Achieve within 0.1% of current accuracy (3.411331% max error)");
    println!();

    let test_values = generate_test_values();
    println!("✅ Generated {} test values", test_values.len());
    println!();

    // Calculate baseline accuracy for current weighted approach
    println!("📊 Calculating baseline accuracy for current weighted approach...");
    let (baseline_max_error, baseline_avg_error, baseline_worst_case) = calculate_weighted_accuracy(&test_values);
    println!("🎯 Current Weighted Baseline:");
    println!("   Magic Constants: 0x5f371ef0, 0x5f37f840");
    println!("   Formula: 0.7 * result1 + 0.3 * result2");
    println!("   Max Error: {:.6}%", baseline_max_error);
    println!("   Avg Error: {:.6}%", baseline_avg_error);
    println!("   Worst Case: {}", baseline_worst_case);
    println!("   Operations: 3 (2 multiplications + 1 addition)");
    println!();

    // Search parameters
    const MAGIC_START: u32 = 0x5f370000;
    const MAGIC_END: u32 = 0x5f380000;
    const MAGIC_STEP: u32 = 0x100; // Start with coarser step for initial exploration

    let target_accuracy = baseline_max_error + 0.1; // Within 0.1% of current

    println!("🔍 Starting simplified dual magic constant search...");
    println!("Search Range: 0x{:08x} to 0x{:08x} (step: 0x{:x})", MAGIC_START, MAGIC_END, MAGIC_STEP);
    println!("Formula: result1 + result2 (addition only)");
    println!("Target Accuracy: ≤{:.6}% (within 0.1% of current)", target_accuracy);
    println!();

    // Use BinaryHeap to maintain top 20 results
    let mut top_results: BinaryHeap<SimplifiedResult> = BinaryHeap::new();
    let start_time = Instant::now();
    let mut combinations_tested = 0;
    let mut improvements_found = 0;

    // Brute-force search through magic constant pairs
    for magic1 in (MAGIC_START..MAGIC_END).step_by(MAGIC_STEP as usize) {
        for magic2 in (magic1..MAGIC_END).step_by(MAGIC_STEP as usize) {
            let result = calculate_simplified_accuracy(magic1, magic2, &test_values);
            combinations_tested += 1;

            // Track improvements within target accuracy
            if result.max_error <= target_accuracy {
                improvements_found += 1;

                if improvements_found <= 10 { // Report first 10 improvements
                    println!("🎉 IMPROVEMENT #{}: {:.6}% max error (0x{:08x}, 0x{:08x})",
                             improvements_found, result.max_error, magic1, magic2);
                }
            }

            // Maintain top 20 results
            if top_results.len() < 20 {
                top_results.push(result);
            } else if let Some(worst) = top_results.peek() {
                if result.max_error < worst.max_error {
                    top_results.pop();
                    top_results.push(result);
                }
            }

            // Progress update every 1000 combinations
            if combinations_tested % 1000 == 0 {
                let elapsed = start_time.elapsed().as_secs_f64();
                let best_error = top_results.peek().map(|r| r.max_error).unwrap_or(f64::INFINITY);
                println!("📊 Progress: {} combinations tested in {:.1}s - Best: {:.6}% - Improvements: {}",
                         combinations_tested, elapsed, best_error, improvements_found);
            }
        }
    }

    let total_time = start_time.elapsed();
    println!();
    println!("✅ Simplified optimization completed in {:.2} seconds", total_time.as_secs_f64());
    println!("   Total combinations tested: {}", combinations_tested);
    println!("   Improvements found: {}", improvements_found);
    println!();

    // Convert heap to sorted vector (best first)
    let mut sorted_results: Vec<_> = top_results.into_iter().collect();
    sorted_results.sort_by(|a, b| a.max_error.partial_cmp(&b.max_error).unwrap());

    if sorted_results.is_empty() {
        println!("❌ No results found - search parameters may need adjustment");
        return;
    }

    println!("🏆 TOP 20 SIMPLIFIED DUAL CONSTANT RESULTS");
    println!("===========================================");
    println!();
    println!("   Rank | Magic1     | Magic2     | Max Error (%) | Avg Error (%) | vs Baseline | Operations");
    println!("   -----|------------|------------|---------------|---------------|-------------|------------");

    for (rank, result) in sorted_results.iter().take(20).enumerate() {
        let comparison = if result.max_error <= target_accuracy { "✅ BETTER" } else { "❌ WORSE" };
        println!("   {:4} | 0x{:08x} | 0x{:08x} | {:11.6} | {:11.6} | {:11} | {:11}",
                 rank + 1, result.magic1, result.magic2,
                 result.max_error, result.avg_error, comparison, "1 (add only)");
    }

    println!();

    // Analyze best result
    if let Some(best) = sorted_results.first() {
        println!("🥇 BEST SIMPLIFIED RESULT:");
        println!("   Magic1: 0x{:08x} ({})", best.magic1, best.magic1);
        println!("   Magic2: 0x{:08x} ({})", best.magic2, best.magic2);
        println!("   Formula: result1 + result2 (addition only)");
        println!("   Max Error: {:.6}%", best.max_error);
        println!("   Avg Error: {:.6}%", best.avg_error);
        println!("   Worst Case Input: {}", best.worst_case_input);
        println!("   Operations: 1 (addition only)");
        println!();

        let accuracy_comparison = best.max_error - baseline_max_error;
        let performance_improvement = ((3.0 - 1.0) / 3.0) * 100.0; // From 3 ops to 1 op

        println!("📊 COMPARISON WITH CURRENT WEIGHTED APPROACH:");
        println!("   Current Max Error: {:.6}%", baseline_max_error);
        println!("   Simplified Max Error: {:.6}%", best.max_error);
        println!("   Accuracy Difference: {:+.6}%", accuracy_comparison);
        println!("   Current Operations: 3 (2 multiplications + 1 addition)");
        println!("   Simplified Operations: 1 (1 addition only)");
        println!("   Performance Improvement: {:.1}% (theoretical)", performance_improvement);
        println!();

        if best.max_error <= target_accuracy {
            println!("✅ SUCCESS: Simplified approach achieves target accuracy!");
            println!("   ✅ Within 0.1% of current accuracy");
            println!("   ✅ Reduces operations from 3 to 1 (66.7% reduction)");
            println!("   ✅ Eliminates multiplication operations entirely");

            println!();
            println!("💡 RECOMMENDED SIMPLIFIED IMPLEMENTATION:");
            println!("   ```rust");
            println!("   #[inline]");
            println!("   fn simplified_dual_fast_inv_sqrt(x: f32) -> f32 {{");
            println!("       if x == 0.0 {{ return 0.0; }}");
            println!("       if x < 0.0 {{ return f32::NAN; }}");
            println!("       if x < 1e-10 {{ return 1.0 / x.sqrt(); }}");
            println!();
            println!("       let bits = x.to_bits();");
            println!("       let result1 = f32::from_bits(0x{:08x} - (bits >> 1));", best.magic1);
            println!("       let result2 = f32::from_bits(0x{:08x} - (bits >> 1));", best.magic2);
            println!("       result1 + result2  // Single addition operation");
            println!("   }}");
            println!("   ```");

            println!();
            println!("📈 PERFORMANCE BENEFITS:");
            println!("   • Eliminates 2 floating-point multiplications");
            println!("   • Reduces from 3 operations to 1 operation");
            println!("   • Maintains dual constant error cancellation benefits");
            println!("   • Estimated performance: ~1.8-2.0 ns/op (vs current 2.71 ns/op)");

        } else {
            println!("❌ RESULT: Simplified approach does not meet accuracy target");
            println!("   Target: ≤{:.6}% (within 0.1% of current)", target_accuracy);
            println!("   Achieved: {:.6}%", best.max_error);
            println!("   Shortfall: {:.6}%", best.max_error - target_accuracy);

            println!();
            println!("🔍 ANALYSIS:");
            println!("   The simplified addition-only approach cannot achieve the required");
            println!("   accuracy without the weighting coefficients. The multiplication");
            println!("   operations in the current approach are essential for optimal");
            println!("   error cancellation between the two magic constants.");
        }
    }

    println!();
    println!("🔬 OPTIMIZATION ANALYSIS:");
    println!("   Search Efficiency: {:.0} combinations/second",
             combinations_tested as f64 / total_time.as_secs_f64());
    println!("   Success Rate: {:.3}% of combinations met target accuracy",
             (improvements_found as f64 / combinations_tested as f64) * 100.0);

    if improvements_found > 0 {
        let best_improvement = baseline_max_error - sorted_results[0].max_error;
        println!("   Best Improvement: {:+.6}% vs baseline", best_improvement);
        println!("   Operations Reduction: 66.7% (from 3 to 1 operation)");
    } else {
        println!("   No combinations achieved target accuracy");
        println!("   Weighted coefficients appear essential for optimal accuracy");
    }
}
