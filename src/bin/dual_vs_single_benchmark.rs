use std::time::Instant;

/// Comprehensive Benchmark: Dual vs Single Magic Constant Performance
/// 
/// This program provides accurate performance measurements comparing:
/// 1. V9 Optimized Single Constant (3.416589% max error)
/// 2. Dual Constant Weighted 0.7 (3.411331% max error)
/// 3. Standard Library (reference)

/// V9 Optimized Single Magic Constant Implementation
#[inline]
fn v9_optimized_single(x: f32) -> f32 {
    if x == 0.0 { return 0.0; }
    if x < 0.0 { return f32::NAN; }
    if x < 1e-10 { return 1.0 / x.sqrt(); }

    // V9 optimized magic constant from brute-force search
    f32::from_bits(0x5f376244 - (x.to_bits() >> 1))
}

/// Dual Magic Constant Weighted 0.7 Implementation
#[inline]
fn dual_weighted_0_7(x: f32) -> f32 {
    if x == 0.0 { return 0.0; }
    if x < 0.0 { return f32::NAN; }
    if x < 1e-10 { return 1.0 / x.sqrt(); }

    let bits = x.to_bits();
    let result1 = f32::from_bits(0x5f371ef0 - (bits >> 1));
    let result2 = f32::from_bits(0x5f37f840 - (bits >> 1));
    0.7 * result1 + 0.3 * result2
}

/// Standard Library Implementation (reference)
#[inline]
fn standard_library(x: f32) -> f32 {
    if x == 0.0 { return 0.0; }
    if x < 0.0 { return f32::NAN; }
    1.0 / x.sqrt()
}

/// Benchmark a function with the given test values
fn benchmark_function<F>(
    name: &str,
    func: F,
    test_values: &[f32],
    iterations: usize,
) -> f64
where
    F: Fn(f32) -> f32,
{
    // Warm-up
    for _ in 0..1000 {
        for &value in test_values.iter().take(100) {
            std::hint::black_box(func(std::hint::black_box(value)));
        }
    }

    let start = Instant::now();
    
    for _ in 0..iterations {
        for &value in test_values {
            std::hint::black_box(func(std::hint::black_box(value)));
        }
    }
    
    let elapsed = start.elapsed();
    let total_ops = iterations * test_values.len();
    let ns_per_op = elapsed.as_nanos() as f64 / total_ops as f64;
    
    println!("{:<25} {:>8.2} ns/op ({:>8} total ops in {:>8} ms)", 
             name, ns_per_op, total_ops, elapsed.as_millis());
    
    ns_per_op
}

/// Calculate accuracy metrics for a function
fn calculate_accuracy<F>(name: &str, func: F, test_values: &[f32])
where
    F: Fn(f32) -> f32,
{
    let mut max_error = 0.0f64;
    let mut total_error = 0.0f64;
    let mut worst_case_input = 0.0f32;
    let mut valid_count = 0;

    for &x in test_values {
        let fast_result = func(x);
        let accurate_result = 1.0 / x.sqrt();

        if fast_result.is_nan() || fast_result.is_infinite() || 
           accurate_result.is_nan() || accurate_result.is_infinite() {
            continue;
        }

        let relative_error = (((fast_result - accurate_result) / accurate_result).abs() * 100.0) as f64;
        
        if relative_error > max_error {
            max_error = relative_error;
            worst_case_input = x;
        }
        
        total_error += relative_error;
        valid_count += 1;
    }

    let avg_error = if valid_count > 0 { total_error / valid_count as f64 } else { 0.0 };

    println!("{:<25} Max: {:>8.6}%, Avg: {:>8.6}%, Worst: {}", 
             name, max_error, avg_error, worst_case_input);
}

/// Generate test values for Vector2 optimization range [0.1, 100]
fn generate_test_values() -> Vec<f32> {
    let mut values = Vec::new();
    
    // Linear distribution
    for i in 0..300 {
        let t = i as f32 / 299.0;
        let value = 0.1 + t * (100.0 - 0.1);
        values.push(value);
    }
    
    // Logarithmic distribution
    for i in 0..300 {
        let t = i as f32 / 299.0;
        let log_min = 0.1f32.ln();
        let log_max = 100.0f32.ln();
        let value = (log_min + t * (log_max - log_min)).exp();
        values.push(value);
    }
    
    // Powers of 2
    for i in -10..=10 {
        let value = 2.0f32.powi(i);
        if value >= 0.1 && value <= 100.0 {
            values.push(value);
        }
    }
    
    // Common vector magnitudes
    let common_values = [
        0.1, 0.2, 0.5, 0.7071067811865476, 1.0, 1.4142135623730951,
        2.0, 3.0, 5.0, 10.0, 20.0, 50.0, 100.0
    ];
    
    for &value in &common_values {
        if value >= 0.1 && value <= 100.0 {
            values.push(value);
        }
    }
    
    // Random values
    use std::collections::HashSet;
    let mut rng_state = 12345u32;
    let mut seen = HashSet::new();
    
    while values.len() < 1000 {
        rng_state = rng_state.wrapping_mul(1664525).wrapping_add(1013904223);
        let random_f32 = (rng_state as f32) / (u32::MAX as f32);
        let value = 0.1 + random_f32 * (100.0 - 0.1);
        
        let key = (value * 1000000.0) as u32;
        if seen.insert(key) {
            values.push(value);
        }
    }
    
    values.truncate(1000);
    values.sort_by(|a, b| a.partial_cmp(b).unwrap());
    values
}

fn main() {
    println!("⚡ Dual vs Single Magic Constant Performance Benchmark");
    println!("======================================================");
    println!("Comparing V9 Optimized Single vs Dual Weighted 0.7 implementations");
    println!();

    let test_values = generate_test_values();
    let iterations = 1000;
    
    println!("📊 Test Configuration:");
    println!("   Test Values: {} (Vector2 range [0.1, 100])", test_values.len());
    println!("   Iterations: {}", iterations);
    println!("   Total Operations per Implementation: {}", test_values.len() * iterations);
    println!();

    println!("🚀 PERFORMANCE BENCHMARK");
    println!("========================");
    
    let std_lib_time = benchmark_function("Standard Library", standard_library, &test_values, iterations);
    let v9_time = benchmark_function("V9 Optimized Single", v9_optimized_single, &test_values, iterations);
    let dual_time = benchmark_function("Dual Weighted 0.7", dual_weighted_0_7, &test_values, iterations);
    
    println!();
    println!("🎯 ACCURACY ANALYSIS");
    println!("====================");
    
    calculate_accuracy("Standard Library", standard_library, &test_values);
    calculate_accuracy("V9 Optimized Single", v9_optimized_single, &test_values);
    calculate_accuracy("Dual Weighted 0.7", dual_weighted_0_7, &test_values);
    
    println!();
    println!("📈 PERFORMANCE COMPARISON");
    println!("=========================");
    
    let v9_vs_std = ((std_lib_time - v9_time) / std_lib_time) * 100.0;
    let dual_vs_std = ((std_lib_time - dual_time) / std_lib_time) * 100.0;
    let dual_vs_v9 = ((dual_time - v9_time) / v9_time) * 100.0;
    
    println!("V9 vs Standard Library:    {:+6.1}% ({:.2} vs {:.2} ns/op)", v9_vs_std, v9_time, std_lib_time);
    println!("Dual vs Standard Library:  {:+6.1}% ({:.2} vs {:.2} ns/op)", dual_vs_std, dual_time, std_lib_time);
    println!("Dual vs V9 Optimized:      {:+6.1}% ({:.2} vs {:.2} ns/op)", dual_vs_v9, dual_time, v9_time);
    
    println!();
    println!("🏆 SUMMARY & RECOMMENDATIONS");
    println!("=============================");
    
    if dual_time <= v9_time * 1.5 { // Within 50% overhead
        println!("✅ DUAL CONSTANT APPROACH RECOMMENDED");
        println!("   • Better accuracy: 3.411331% vs 3.416589% max error");
        println!("   • Acceptable performance overhead: {:.1}%", dual_vs_v9);
        println!("   • Suitable for accuracy-critical Vector2 operations");
    } else {
        println!("⚠️  PERFORMANCE OVERHEAD TOO HIGH");
        println!("   • Accuracy improvement: 0.1539% reduction in max error");
        println!("   • Performance cost: {:.1}% slower than V9", dual_vs_v9);
        println!("   • Consider V9 single constant for performance-critical code");
    }
    
    println!();
    println!("💡 USE CASE RECOMMENDATIONS:");
    println!("   🏃 High Performance (Game Loops):     V9 Optimized Single");
    println!("   🎯 High Accuracy (Physics/Graphics):  Dual Weighted 0.7");
    println!("   📐 Scientific Computing:              Standard Library");
    
    println!();
    println!("🔬 TECHNICAL DETAILS:");
    println!("   V9 Magic Constant:     0x5f376244");
    println!("   Dual Magic Constants:  0x5f371ef0, 0x5f37f840");
    println!("   Dual Combination:      0.7 * result1 + 0.3 * result2");
    println!("   Accuracy Improvement:  {:.4}% reduction in max error", 
             ((3.416589 - 3.411331) / 3.416589) * 100.0);
}
