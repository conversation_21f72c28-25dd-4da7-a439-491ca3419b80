use std::time::Instant;

/// Fine-Tuned Dual Magic Constant Optimization
/// 
/// Based on initial exploration results, this program focuses on the most promising
/// approach: weighted_0.7 combination with fine-grained search around the best
/// magic constant pairs found in the initial exploration.

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
struct DualMagicResult {
    magic1: u32,
    magic2: u32,
    max_error: f64,
    avg_error: f64,
    worst_case_input: f32,
}

/// Optimized weighted combination (alpha = 0.7) implementation
#[inline]
fn dual_weighted_0_7(x: f32, magic1: u32, magic2: u32) -> f32 {
    if x == 0.0 { return 0.0; }
    if x < 0.0 { return f32::NAN; }
    if x < 1e-10 { return 1.0 / x.sqrt(); }

    let bits = x.to_bits();
    let result1 = f32::from_bits(magic1 - (bits >> 1));
    let result2 = f32::from_bits(magic2 - (bits >> 1));
    0.7 * result1 + 0.3 * result2
}

/// Calculate accuracy metrics for dual magic constants
fn calculate_dual_accuracy(magic1: u32, magic2: u32, test_values: &[f32]) -> DualMagicResult {
    let mut max_error = 0.0f64;
    let mut total_error = 0.0f64;
    let mut worst_case_input = 0.0f32;
    let mut valid_count = 0;

    for &x in test_values {
        let fast_result = dual_weighted_0_7(x, magic1, magic2);
        let accurate_result = 1.0 / x.sqrt();

        if fast_result.is_nan() || fast_result.is_infinite() || 
           accurate_result.is_nan() || accurate_result.is_infinite() {
            continue;
        }

        let relative_error = (((fast_result - accurate_result) / accurate_result).abs() * 100.0) as f64;
        
        if relative_error > max_error {
            max_error = relative_error;
            worst_case_input = x;
        }
        
        total_error += relative_error;
        valid_count += 1;
    }

    let avg_error = if valid_count > 0 { total_error / valid_count as f64 } else { f64::INFINITY };

    DualMagicResult {
        magic1,
        magic2,
        max_error,
        avg_error,
        worst_case_input,
    }
}

/// Generate test values for Vector2 optimization range [0.1, 100]
fn generate_test_values() -> Vec<f32> {
    let mut values = Vec::new();
    
    // Linear distribution
    for i in 0..300 {
        let t = i as f32 / 299.0;
        let value = 0.1 + t * (100.0 - 0.1);
        values.push(value);
    }
    
    // Logarithmic distribution
    for i in 0..300 {
        let t = i as f32 / 299.0;
        let log_min = 0.1f32.ln();
        let log_max = 100.0f32.ln();
        let value = (log_min + t * (log_max - log_min)).exp();
        values.push(value);
    }
    
    // Powers of 2
    for i in -10..=10 {
        let value = 2.0f32.powi(i);
        if value >= 0.1 && value <= 100.0 {
            values.push(value);
        }
    }
    
    // Common vector magnitudes
    let common_values = [
        0.1, 0.2, 0.5, 0.7071067811865476, 1.0, 1.4142135623730951,
        2.0, 3.0, 5.0, 10.0, 20.0, 50.0, 100.0
    ];
    
    for &value in &common_values {
        if value >= 0.1 && value <= 100.0 {
            values.push(value);
        }
    }
    
    // Random values
    use std::collections::HashSet;
    let mut rng_state = 12345u32;
    let mut seen = HashSet::new();
    
    while values.len() < 900 {
        rng_state = rng_state.wrapping_mul(1664525).wrapping_add(1013904223);
        let random_f32 = (rng_state as f32) / (u32::MAX as f32);
        let value = 0.1 + random_f32 * (100.0 - 0.1);
        
        let key = (value * 1000000.0) as u32;
        if seen.insert(key) {
            values.push(value);
        }
    }
    
    values.truncate(900);
    values.sort_by(|a, b| a.partial_cmp(b).unwrap());
    values
}

fn main() {
    println!("🎯 Fine-Tuned Dual Magic Constant Optimization");
    println!("===============================================");
    println!("Focusing on weighted_0.7 approach with fine-grained search");
    println!("Target: Improve upon V9 optimized (3.416589% max error)");
    println!();

    let test_values = generate_test_values();
    println!("✅ Generated {} test values", test_values.len());
    println!();

    // Best pairs from initial exploration (weighted_0.7 method)
    let promising_pairs = [
        (0x5f371f00, 0x5f37f800), // Best from initial search
        (0x5f372800, 0x5f37e300), // Second best
        (0x5f371c00, 0x5f37ff00), // Fourth best
        (0x5f372200, 0x5f37f100), // Fifth best
    ];

    println!("🔍 Fine-tuning around {} promising pairs...", promising_pairs.len());
    
    let mut all_results = Vec::new();
    let start_time = Instant::now();
    let mut combinations_tested = 0;

    // Fine-grained search around each promising pair
    for &(base_magic1, base_magic2) in &promising_pairs {
        println!("🔬 Exploring around 0x{:08x}, 0x{:08x}...", base_magic1, base_magic2);
        
        // Search in ±0x100 range around each base constant with step 0x10
        for offset1 in (-0x100i32..=0x100).step_by(0x10) {
            for offset2 in (-0x100i32..=0x100).step_by(0x10) {
                let magic1 = (base_magic1 as i64 + offset1 as i64) as u32;
                let magic2 = (base_magic2 as i64 + offset2 as i64) as u32;
                
                // Ensure we stay within reasonable bounds
                if magic1 < 0x5f370000 || magic1 > 0x5f380000 ||
                   magic2 < 0x5f370000 || magic2 > 0x5f380000 {
                    continue;
                }
                
                let result = calculate_dual_accuracy(magic1, magic2, &test_values);
                
                // Only keep results that improve upon V9
                if result.max_error < 3.416589 {
                    all_results.push(result);
                }
                
                combinations_tested += 1;
            }
        }
    }

    let total_time = start_time.elapsed();
    println!();
    println!("✅ Fine-tuning completed in {:.2} seconds", total_time.as_secs_f64());
    println!("   Combinations tested: {}", combinations_tested);
    println!("   Improved results found: {}", all_results.len());
    println!();

    if all_results.is_empty() {
        println!("❌ No fine-tuned combinations improve upon V9 baseline");
        println!("   Initial exploration results may represent the optimum");
        return;
    }

    // Sort by accuracy
    all_results.sort_by(|a, b| a.max_error.partial_cmp(&b.max_error).unwrap());

    println!("🏆 FINE-TUNED DUAL MAGIC CONSTANT RESULTS");
    println!("==========================================");
    println!();

    println!("🎯 TOP 10 FINE-TUNED COMBINATIONS:");
    println!("   Rank | Magic1     | Magic2     | Max Error (%) | Avg Error (%) | Improvement");
    println!("   -----|------------|------------|---------------|---------------|------------");
    
    for (rank, result) in all_results.iter().take(10).enumerate() {
        let improvement = ((3.416589 - result.max_error) / 3.416589) * 100.0;
        println!("   {:4} | 0x{:08x} | 0x{:08x} | {:11.6} | {:11.6} | {:+9.4}%", 
                 rank + 1, result.magic1, result.magic2, 
                 result.max_error, result.avg_error, improvement);
    }
    println!();

    // Analyze best result
    if let Some(best) = all_results.first() {
        println!("🥇 OPTIMAL DUAL CONSTANT RESULT:");
        println!("   Magic1: 0x{:08x} ({})", best.magic1, best.magic1);
        println!("   Magic2: 0x{:08x} ({})", best.magic2, best.magic2);
        println!("   Max Error: {:.6}%", best.max_error);
        println!("   Avg Error: {:.6}%", best.avg_error);
        println!("   Worst Case Input: {}", best.worst_case_input);
        println!();

        let accuracy_improvement = ((3.416589 - best.max_error) / 3.416589) * 100.0;
        let performance_overhead = ((3.0 - 2.06) / 2.06) * 100.0; // Estimated 3.0 ns/op

        println!("📊 COMPARISON WITH V9 OPTIMIZED:");
        println!("   V9 Max Error: 3.416589%");
        println!("   Dual Max Error: {:.6}%", best.max_error);
        println!("   Accuracy Improvement: {:.4}% reduction", accuracy_improvement);
        println!("   Performance: ~3.0 ns/op vs V9's 2.06 ns/op");
        println!("   Performance Overhead: {:.1}%", performance_overhead);
        println!();

        if best.max_error < 3.416589 {
            println!("✅ SUCCESS: Dual constant approach achieves better accuracy!");
            
            if performance_overhead <= 50.0 {
                println!("   ✅ Performance overhead acceptable for accuracy gain");
            } else {
                println!("   ⚠️  Performance overhead significant - evaluate trade-off");
            }
            
            println!();
            println!("💡 IMPLEMENTATION RECOMMENDATION:");
            println!("   Consider implementing dual constant approach for applications");
            println!("   where accuracy is more critical than maximum performance.");
            println!();
            println!("   Implementation:");
            println!("   ```rust");
            println!("   #[inline]");
            println!("   fn dual_fast_inv_sqrt(x: f32) -> f32 {{");
            println!("       if x == 0.0 {{ return 0.0; }}");
            println!("       if x < 0.0 {{ return f32::NAN; }}");
            println!("       if x < 1e-10 {{ return 1.0 / x.sqrt(); }}");
            println!();
            println!("       let bits = x.to_bits();");
            println!("       let result1 = f32::from_bits(0x{:08x} - (bits >> 1));", best.magic1);
            println!("       let result2 = f32::from_bits(0x{:08x} - (bits >> 1));", best.magic2);
            println!("       0.7 * result1 + 0.3 * result2");
            println!("   }}");
            println!("   ```");
        } else {
            println!("❌ UNEXPECTED: Fine-tuning did not improve upon initial results");
        }
    }

    println!();
    println!("🔬 FINE-TUNING ANALYSIS:");
    println!("   Search Density: {:.0} combinations/second", 
             combinations_tested as f64 / total_time.as_secs_f64());
    println!("   Success Rate: {:.1}% of combinations improved upon V9", 
             (all_results.len() as f64 / combinations_tested as f64) * 100.0);
    
    if !all_results.is_empty() {
        let best_improvement = ((3.416589 - all_results[0].max_error) / 3.416589) * 100.0;
        println!("   Best Improvement: {:.4}% reduction in max error", best_improvement);
        println!("   Accuracy Range: {:.6}% to {:.6}%", 
                 all_results[0].max_error, 
                 all_results.last().unwrap().max_error);
    }
}
