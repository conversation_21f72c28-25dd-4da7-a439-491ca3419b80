//! Comprehensive 4D floating-point vector implementation for quaternions and homogeneous coordinates.
//!
//! This module provides a complete 4D vector implementation optimized for quaternion operations,
//! homogeneous coordinates in 3D graphics, color representation (RGBA), and 4D mathematical
//! calculations commonly used in advanced graphics programming and scientific computing.

use std::fmt;
use super::Vector3;
use super::fast_inv_sqrt::fast_inv_sqrt;

/// A 4D vector using floating-point coordinates.
///
/// This struct represents a point or direction in 4D space with floating-point x, y, z, and w components.
/// It provides comprehensive 4D mathematical operations optimized for quaternion calculations,
/// homogeneous coordinates, color representation, and advanced mathematical computations.
///
/// ## Use Cases
///
/// Vector4 is ideal for:
/// - **Quaternion Operations**: Rotation representation, orientation calculations
/// - **Homogeneous Coordinates**: 3D graphics transformations, projection matrices
/// - **Color Representation**: RGBA color values with alpha channel
/// - **4D Mathematics**: Hyperspace calculations, advanced algorithms
/// - **Graphics Programming**: Shader uniforms, vertex attributes
///
/// ## Component Interpretations
///
/// Depending on the use case, components can represent:
/// - **Quaternion**: (x, y, z, w) = (i, j, k, real)
/// - **Color**: (x, y, z, w) = (r, g, b, a)
/// - **Homogeneous**: (x, y, z, w) = (x, y, z, 1) for points, (x, y, z, 0) for vectors
/// - **4D Space**: (x, y, z, w) = spatial coordinates in 4D
///
/// ## Performance Features
///
/// - **Fast Inverse Square Root**: Uses optimized Quake III algorithm for normalization
/// - **SIMD-Friendly**: Structure layout optimized for vectorization
/// - **Inline Methods**: Performance-critical operations marked with #[inline]
/// - **Memory Efficient**: Compact 16-byte representation (4 × f32)
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct Vector4 {
    /// The vector's X component (first coordinate).
    pub x: f32,
    /// The vector's Y component (second coordinate).
    pub y: f32,
    /// The vector's Z component (third coordinate).
    pub z: f32,
    /// The vector's W component (fourth coordinate).
    pub w: f32,
}

impl Vector4 {
    /// ### Zero vector constant -> Vector4(0.0, 0.0, 0.0, 0.0).
    ///
    /// Represents the additive identity for vector operations and the origin point in 4D space.
    /// This vector has zero magnitude and no defined direction.
    pub const ZERO: Vector4 = Vector4 { x: 0.0, y: 0.0, z: 0.0, w: 0.0 };

    /// ### Unit vector constant -> Vector4(1.0, 1.0, 1.0, 1.0).
    ///
    /// A vector with all components set to 1.0, commonly used for uniform scaling
    /// and as a reference vector. Has magnitude 2.0.
    pub const ONE: Vector4 = Vector4 { x: 1.0, y: 1.0, z: 1.0, w: 1.0 };

    /// ### Identity quaternion constant -> Vector4(0.0, 0.0, 0.0, 1.0).
    ///
    /// Represents no rotation when used as a quaternion. The w component is 1.0
    /// while x, y, z components are 0.0, representing the multiplicative identity for rotations.
    pub const IDENTITY: Vector4 = Vector4 { x: 0.0, y: 0.0, z: 0.0, w: 1.0 };

    /// ### Creates a new Vector4 with the specified x, y, z, and w components.
    ///
    /// This is the primary constructor for creating 4D vectors with explicit coordinate values.
    /// All components can be any finite floating-point value, including negative numbers and zero.
    ///
    /// # Arguments
    /// * `x` - The first component (X-coordinate)
    /// * `y` - The second component (Y-coordinate)
    /// * `z` - The third component (Z-coordinate)
    /// * `w` - The fourth component (W-coordinate)
    ///
    /// # Returns
    /// A new Vector4 instance with the specified components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let quaternion = Vector4::new(0.0, 0.0, 0.0, 1.0);
    /// assert_eq!(quaternion.x, 0.0);
    /// assert_eq!(quaternion.w, 1.0);
    ///
    /// // Color representation (RGBA)
    /// let red = Vector4::new(1.0, 0.0, 0.0, 1.0);
    /// assert_eq!(red.x, 1.0); // Red component
    /// assert_eq!(red.w, 1.0); // Alpha component
    /// ```
    #[inline]
    pub const fn new(x: f32, y: f32, z: f32, w: f32) -> Self {
        Vector4 { x, y, z, w }
    }

    /// ### Creates a Vector4 from a Vector3 by adding a w component.
    ///
    /// This constructor allows easy conversion from 3D to 4D coordinates by specifying
    /// the w component explicitly. Useful for homogeneous coordinates and quaternion construction.
    ///
    /// # Arguments
    /// * `v3` - The 3D vector providing x, y, and z components
    /// * `w` - The w component to add
    ///
    /// # Returns
    /// A new Vector4 with x, y, z from the Vector3 and the specified w component.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector3, Vector4};
    /// let pos_3d = Vector3::new(3.0, 4.0, 5.0);
    /// let homogeneous_point = Vector4::from_vector3(pos_3d, 1.0);
    /// assert_eq!(homogeneous_point, Vector4::new(3.0, 4.0, 5.0, 1.0));
    ///
    /// // For direction vectors in homogeneous coordinates
    /// let direction = Vector4::from_vector3(pos_3d, 0.0);
    /// assert_eq!(direction.w, 0.0);
    /// ```
    #[inline]
    pub fn from_vector3(v3: Vector3, w: f32) -> Self {
        Vector4::new(v3.x, v3.y, v3.z, w)
    }

    /// ### Calculates the length (magnitude) of the vector.
    ///
    /// Uses fast inverse square root for optimal performance while maintaining
    /// acceptable accuracy for most 4D mathematical applications.
    ///
    /// # Returns
    /// The length of the vector as a floating-point number.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let v = Vector4::new(2.0, 0.0, 0.0, 0.0);
    /// assert!((v.length() - 2.0).abs() < 0.001);
    ///
    /// let unit = Vector4::new(0.5, 0.5, 0.5, 0.5);
    /// assert!((unit.length() - 1.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn length(self) -> f32 {
        let length_sq = self.length_squared();
        if length_sq == 0.0 {
            0.0
        } else {
            1.0 / fast_inv_sqrt(length_sq)
        }
    }

    /// ### Calculates the squared length of the vector.
    ///
    /// This is more efficient than length() as it avoids the square root calculation.
    /// Useful for distance comparisons and magnitude-based operations.
    ///
    /// # Returns
    /// The squared length of the vector (x² + y² + z² + w²).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let v = Vector4::new(2.0, 0.0, 0.0, 0.0);
    /// assert_eq!(v.length_squared(), 4.0); // 2² + 0² + 0² + 0² = 4
    /// ```
    #[inline]
    pub fn length_squared(self) -> f32 {
        self.x * self.x + self.y * self.y + self.z * self.z + self.w * self.w
    }

    /// ### Returns a normalized copy of the vector.
    ///
    /// Creates a new vector with the same direction but unit length (magnitude = 1).
    /// Uses fast inverse square root for optimal performance. Returns zero vector
    /// if the original vector has zero length.
    ///
    /// # Returns
    /// A new Vector4 with unit length in the same direction, or zero vector if length is zero.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let v = Vector4::new(2.0, 0.0, 0.0, 0.0);
    /// let normalized = v.normalized();
    /// assert!((normalized.length() - 1.0).abs() < 0.001);
    /// assert!((normalized.x - 1.0).abs() < 0.001);
    /// ```
    #[inline]
    pub fn normalized(self) -> Vector4 {
        let length_sq = self.length_squared();
        if length_sq == 0.0 {
            Vector4::ZERO
        } else {
            let inv_length = fast_inv_sqrt(length_sq);
            Vector4::new(
                self.x * inv_length,
                self.y * inv_length,
                self.z * inv_length,
                self.w * inv_length,
            )
        }
    }

    /// ### Calculates the dot product with another vector.
    ///
    /// The dot product is useful for determining the relationship between two vectors,
    /// such as the angle between them or projecting one vector onto another.
    ///
    /// # Arguments
    /// * `other` - The other vector to compute the dot product with
    ///
    /// # Returns
    /// The dot product as a floating-point number.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let v1 = Vector4::new(1.0, 2.0, 3.0, 4.0);
    /// let v2 = Vector4::new(5.0, 6.0, 7.0, 8.0);
    /// assert_eq!(v1.dot(v2), 70.0); // 1*5 + 2*6 + 3*7 + 4*8 = 5 + 12 + 21 + 32 = 70
    ///
    /// // Identity quaternion dot product with itself
    /// let identity = Vector4::IDENTITY;
    /// assert_eq!(identity.dot(identity), 1.0);
    /// ```
    #[inline]
    pub fn dot(self, other: Vector4) -> f32 {
        self.x * other.x + self.y * other.y + self.z * other.z + self.w * other.w
    }

    /// ### Linear interpolation between this vector and another vector.
    ///
    /// Returns a vector that is linearly interpolated between this vector and the target vector
    /// based on the weight parameter. When weight is 0.0, returns this vector. When weight is 1.0,
    /// returns the target vector.
    ///
    /// # Arguments
    /// * `to` - The target vector to interpolate towards
    /// * `weight` - The interpolation weight (typically between 0.0 and 1.0)
    ///
    /// # Returns
    /// The interpolated vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let from = Vector4::new(0.0, 0.0, 0.0, 0.0);
    /// let to = Vector4::new(10.0, 20.0, 30.0, 40.0);
    /// let mid = from.lerp(to, 0.5);
    /// assert_eq!(mid, Vector4::new(5.0, 10.0, 15.0, 20.0));
    /// ```
    #[inline]
    pub fn lerp(self, to: Vector4, weight: f32) -> Vector4 {
        Vector4::new(
            self.x + (to.x - self.x) * weight,
            self.y + (to.y - self.y) * weight,
            self.z + (to.z - self.z) * weight,
            self.w + (to.w - self.w) * weight,
        )
    }

    /// ### Returns a vector with the absolute values of the components.
    ///
    /// Creates a new vector where all x, y, z, and w components are their absolute values.
    /// This is useful for distance calculations and ensuring positive coordinates.
    ///
    /// # Returns
    /// A new Vector4 with absolute values of all components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let v = Vector4::new(-1.0, -2.0, -3.0, -4.0);
    /// let abs_v = v.abs();
    /// assert_eq!(abs_v, Vector4::new(1.0, 2.0, 3.0, 4.0));
    /// ```
    #[inline]
    pub fn abs(self) -> Vector4 {
        Vector4::new(self.x.abs(), self.y.abs(), self.z.abs(), self.w.abs())
    }

    /// ### Checks if this vector is approximately equal to another vector.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    /// Two vectors are considered approximately equal if all components are within
    /// the epsilon threshold.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// `true` if the vectors are approximately equal, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let v1 = Vector4::new(1.0, 2.0, 3.0, 4.0);
    /// let v2 = Vector4::new(1.0000001, 2.0000001, 3.0000001, 4.0000001);
    /// assert!(v1.is_equal_approx(v2));
    ///
    /// let v3 = Vector4::new(1.1, 2.0, 3.0, 4.0);
    /// assert!(!v1.is_equal_approx(v3));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Vector4) -> bool {
        const EPSILON: f32 = 1e-5;
        (self.x - other.x).abs() < EPSILON
            && (self.y - other.y).abs() < EPSILON
            && (self.z - other.z).abs() < EPSILON
            && (self.w - other.w).abs() < EPSILON
    }

    /// ### Checks if the vector is normalized (has a length of approximately 1.0).
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    /// A vector is considered normalized if its length is within epsilon of 1.0.
    ///
    /// # Returns
    /// `true` if the vector is approximately normalized, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let normalized = Vector4::new(1.0, 0.0, 0.0, 0.0);
    /// assert!(normalized.is_normalized());
    ///
    /// let not_normalized = Vector4::new(2.0, 0.0, 0.0, 0.0);
    /// assert!(!not_normalized.is_normalized());
    /// ```
    #[inline]
    pub fn is_normalized(self) -> bool {
        const EPSILON: f32 = 1e-4;
        (self.length_squared() - 1.0).abs() < EPSILON
    }

    /// ### Checks if all components of the vector are finite numbers.
    ///
    /// Returns `false` if any component is infinite or NaN.
    ///
    /// # Returns
    /// `true` if all x, y, z, and w components are finite, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let finite = Vector4::new(1.0, 2.0, 3.0, 4.0);
    /// assert!(finite.is_finite());
    ///
    /// let infinite = Vector4::new(f32::INFINITY, 2.0, 3.0, 4.0);
    /// assert!(!infinite.is_finite());
    /// ```
    #[inline]
    pub fn is_finite(self) -> bool {
        self.x.is_finite() && self.y.is_finite() && self.z.is_finite() && self.w.is_finite()
    }

    /// ### Checks if the vector is approximately zero.
    ///
    /// Uses a small epsilon value to account for floating-point precision errors.
    /// A vector is considered approximately zero if all components are within
    /// epsilon of zero.
    ///
    /// # Returns
    /// `true` if the vector is approximately zero, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4;
    /// let zero = Vector4::ZERO;
    /// assert!(zero.is_zero_approx());
    ///
    /// let almost_zero = Vector4::new(1e-6, -1e-6, 1e-6, -1e-6);
    /// assert!(almost_zero.is_zero_approx());
    /// ```
    #[inline]
    pub fn is_zero_approx(self) -> bool {
        const EPSILON: f32 = 1e-5;
        self.x.abs() < EPSILON
            && self.y.abs() < EPSILON
            && self.z.abs() < EPSILON
            && self.w.abs() < EPSILON
    }

    // ============================================================================
    // CONVERSION METHODS
    // ============================================================================

    /// ### Converts this floating-point vector to an integer Vector4i.
    ///
    /// Each component is converted using Rust's default float-to-integer conversion,
    /// which truncates towards zero. This is useful for converting floating-point
    /// color values to integer color values or continuous 4D coordinates to discrete ones.
    ///
    /// # Returns
    /// A new Vector4i with components converted to integers.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector4, Vector4i};
    /// let float_vec = Vector4::new(3.7, -2.9, 5.1, 1.8);
    /// let int_vec = float_vec.to_vector4i();
    /// assert_eq!(int_vec, Vector4i::new(3, -2, 5, 1)); // Truncated towards zero
    ///
    /// // Useful for color conversion (0.0-1.0 range to 0-255 range)
    /// let color_float = Vector4::new(0.8, 0.4, 0.2, 1.0);
    /// let color_scaled = color_float * 255.0;
    /// let color_int = color_scaled.to_vector4i();
    /// assert_eq!(color_int, Vector4i::new(204, 102, 51, 255));
    /// ```
    #[inline]
    pub fn to_vector4i(self) -> super::Vector4i {
        super::Vector4i::new(self.x as i32, self.y as i32, self.z as i32, self.w as i32)
    }

    /// ### Converts this 4D vector to a 3D Vector3 by dropping the w component.
    ///
    /// This is useful for converting homogeneous coordinates back to 3D coordinates,
    /// extracting RGB from RGBA colors, or reducing 4D data to 3D space.
    ///
    /// # Returns
    /// A new Vector3 with only the x, y, and z components from this vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector4, Vector3};
    /// let homogeneous = Vector4::new(10.0, 20.0, 30.0, 1.0);
    /// let pos_3d = homogeneous.to_vector3();
    /// assert_eq!(pos_3d, Vector3::new(10.0, 20.0, 30.0));
    ///
    /// // Useful for extracting RGB from RGBA color
    /// let rgba_color = Vector4::new(0.8, 0.4, 0.2, 1.0);
    /// let rgb_color = rgba_color.to_vector3();
    /// assert_eq!(rgb_color, Vector3::new(0.8, 0.4, 0.2));
    /// ```
    #[inline]
    pub fn to_vector3(self) -> Vector3 {
        Vector3::new(self.x, self.y, self.z)
    }

    /// ### Converts this 4D vector to a 2D Vector2 by dropping the z and w components.
    ///
    /// This is useful for extracting 2D information from 4D data, converting
    /// homogeneous coordinates to 2D screen coordinates, or extracting RG from RGBA colors.
    ///
    /// # Returns
    /// A new Vector2 with only the x and y components from this vector.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector4, Vector2};
    /// let homogeneous = Vector4::new(10.0, 20.0, 30.0, 1.0);
    /// let pos_2d = homogeneous.to_vector2();
    /// assert_eq!(pos_2d, Vector2::new(10.0, 20.0));
    ///
    /// // Useful for extracting RG from RGBA color
    /// let rgba_color = Vector4::new(0.8, 0.4, 0.2, 1.0);
    /// let rg_color = rgba_color.to_vector2();
    /// assert_eq!(rg_color, Vector2::new(0.8, 0.4));
    /// ```
    #[inline]
    pub fn to_vector2(self) -> super::Vector2 {
        super::Vector2::new(self.x, self.y)
    }
}

// ============================================================================
// ARITHMETIC OPERATIONS
// ============================================================================

impl std::ops::Add for Vector4 {
    type Output = Vector4;

    #[inline]
    fn add(self, other: Vector4) -> Vector4 {
        Vector4::new(
            self.x + other.x,
            self.y + other.y,
            self.z + other.z,
            self.w + other.w,
        )
    }
}

impl std::ops::Sub for Vector4 {
    type Output = Vector4;

    #[inline]
    fn sub(self, other: Vector4) -> Vector4 {
        Vector4::new(
            self.x - other.x,
            self.y - other.y,
            self.z - other.z,
            self.w - other.w,
        )
    }
}

impl std::ops::Mul<f32> for Vector4 {
    type Output = Vector4;

    #[inline]
    fn mul(self, scalar: f32) -> Vector4 {
        Vector4::new(
            self.x * scalar,
            self.y * scalar,
            self.z * scalar,
            self.w * scalar,
        )
    }
}

impl std::ops::Mul<Vector4> for f32 {
    type Output = Vector4;

    #[inline]
    fn mul(self, vector: Vector4) -> Vector4 {
        Vector4::new(
            self * vector.x,
            self * vector.y,
            self * vector.z,
            self * vector.w,
        )
    }
}

impl std::ops::Div<f32> for Vector4 {
    type Output = Vector4;

    #[inline]
    fn div(self, scalar: f32) -> Vector4 {
        Vector4::new(
            self.x / scalar,
            self.y / scalar,
            self.z / scalar,
            self.w / scalar,
        )
    }
}

impl std::ops::Neg for Vector4 {
    type Output = Vector4;

    #[inline]
    fn neg(self) -> Vector4 {
        Vector4::new(-self.x, -self.y, -self.z, -self.w)
    }
}

impl fmt::Display for Vector4 {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "({}, {}, {}, {})", self.x, self.y, self.z, self.w)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_vector4_creation() {
        let v = Vector4::new(1.0, 2.0, 3.0, 4.0);
        assert_eq!(v.x, 1.0);
        assert_eq!(v.y, 2.0);
        assert_eq!(v.z, 3.0);
        assert_eq!(v.w, 4.0);
    }

    #[test]
    fn test_vector4_from_vector3() {
        let v3 = Vector3::new(1.0, 2.0, 3.0);
        let v4 = Vector4::from_vector3(v3, 4.0);
        assert_eq!(v4, Vector4::new(1.0, 2.0, 3.0, 4.0));
    }

    #[test]
    fn test_vector4_constants() {
        assert_eq!(Vector4::ZERO, Vector4::new(0.0, 0.0, 0.0, 0.0));
        assert_eq!(Vector4::ONE, Vector4::new(1.0, 1.0, 1.0, 1.0));
        assert_eq!(Vector4::IDENTITY, Vector4::new(0.0, 0.0, 0.0, 1.0));
    }

    #[test]
    fn test_vector4_length() {
        let v = Vector4::new(2.0, 0.0, 0.0, 0.0);
        assert!((v.length() - 2.0).abs() < 0.001);
        assert_eq!(v.length_squared(), 4.0);

        let unit = Vector4::new(0.5, 0.5, 0.5, 0.5);
        assert!((unit.length() - 1.0).abs() < 0.001);

        let zero = Vector4::ZERO;
        assert_eq!(zero.length(), 0.0);
        assert_eq!(zero.length_squared(), 0.0);
    }

    #[test]
    fn test_vector4_normalization() {
        let v = Vector4::new(2.0, 0.0, 0.0, 0.0);
        let normalized = v.normalized();
        assert!((normalized.length() - 1.0).abs() < 0.001);
        assert!((normalized.x - 1.0).abs() < 0.001);
        assert!(normalized.is_normalized());

        // Zero vector normalization should return zero
        let zero_normalized = Vector4::ZERO.normalized();
        assert_eq!(zero_normalized, Vector4::ZERO);
    }

    #[test]
    fn test_vector4_dot_product() {
        let v1 = Vector4::new(1.0, 2.0, 3.0, 4.0);
        let v2 = Vector4::new(5.0, 6.0, 7.0, 8.0);
        assert_eq!(v1.dot(v2), 70.0); // 1*5 + 2*6 + 3*7 + 4*8 = 5 + 12 + 21 + 32 = 70

        // Identity quaternion dot product with itself
        let identity = Vector4::IDENTITY;
        assert_eq!(identity.dot(identity), 1.0);
    }

    #[test]
    fn test_vector4_interpolation() {
        let from = Vector4::new(0.0, 0.0, 0.0, 0.0);
        let to = Vector4::new(10.0, 20.0, 30.0, 40.0);
        let mid = from.lerp(to, 0.5);
        assert_eq!(mid, Vector4::new(5.0, 10.0, 15.0, 20.0));
    }

    #[test]
    fn test_vector4_utility_functions() {
        let v = Vector4::new(-1.0, -2.0, -3.0, -4.0);
        assert_eq!(v.abs(), Vector4::new(1.0, 2.0, 3.0, 4.0));
    }

    #[test]
    fn test_vector4_boolean_checks() {
        let v1 = Vector4::new(1.0, 2.0, 3.0, 4.0);
        let v2 = Vector4::new(1.0000001, 2.0000001, 3.0000001, 4.0000001);
        assert!(v1.is_equal_approx(v2));

        let normalized = Vector4::new(1.0, 0.0, 0.0, 0.0);
        assert!(normalized.is_normalized());

        let finite = Vector4::new(1.0, 2.0, 3.0, 4.0);
        assert!(finite.is_finite());

        let infinite = Vector4::new(f32::INFINITY, 2.0, 3.0, 4.0);
        assert!(!infinite.is_finite());

        assert!(Vector4::ZERO.is_zero_approx());
        assert!(!Vector4::ONE.is_zero_approx());
    }

    #[test]
    fn test_vector4_conversion_methods() {
        let float_vec = Vector4::new(3.7, -2.9, 5.1, 1.8);
        let int_vec = float_vec.to_vector4i();
        assert_eq!(int_vec, super::super::Vector4i::new(3, -2, 5, 1));

        let vec_3d = float_vec.to_vector3();
        assert_eq!(vec_3d, Vector3::new(3.7, -2.9, 5.1));

        let vec_2d = float_vec.to_vector2();
        assert_eq!(vec_2d, super::super::Vector2::new(3.7, -2.9));
    }

    #[test]
    fn test_vector4_color_operations() {
        // Test RGBA color operations
        let red = Vector4::new(1.0, 0.0, 0.0, 1.0);
        let green = Vector4::new(0.0, 1.0, 0.0, 1.0);

        // Test that color vectors work as expected
        assert_eq!(red.x, 1.0); // Red component
        assert_eq!(red.w, 1.0); // Alpha component
        assert_eq!(green.y, 1.0); // Green component

        // Test color normalization (for HDR colors)
        let hdr_color = Vector4::new(2.0, 3.0, 4.0, 1.0);
        let normalized_color = hdr_color.normalized();
        assert!((normalized_color.length() - 1.0).abs() < 0.001);
    }

    #[test]
    fn test_vector4_homogeneous_coordinates() {
        // Test homogeneous coordinate operations
        let point = Vector4::new(10.0, 20.0, 30.0, 1.0); // Point in 3D space
        let direction = Vector4::new(1.0, 0.0, 0.0, 0.0); // Direction vector

        assert_eq!(point.w, 1.0); // Points have w = 1
        assert_eq!(direction.w, 0.0); // Directions have w = 0

        // Convert back to 3D
        let point_3d = point.to_vector3();
        assert_eq!(point_3d, Vector3::new(10.0, 20.0, 30.0));
    }

    #[test]
    fn test_vector4_edge_cases() {
        // Very small values
        let small = Vector4::new(1e-10, 1e-10, 1e-10, 1e-10);
        assert!(small.is_zero_approx());

        // Very large values
        let large = Vector4::new(1e10, 1e10, 1e10, 1e10);
        assert!(large.is_finite());

        // NaN handling
        let nan_vec = Vector4::new(f32::NAN, 1.0, 2.0, 3.0);
        assert!(!nan_vec.is_finite());
        assert!(!nan_vec.is_normalized());
    }
}
