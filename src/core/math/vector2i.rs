//! Comprehensive 2D integer vector implementation for grid-based operations.
//!
//! This module provides a complete 2D integer vector implementation optimized for
//! discrete mathematics, grid-based games, tile systems, pixel-perfect positioning,
//! and UI layout calculations commonly used in 2D game development.

use std::fmt;
use super::Vector2;

/// A 2D vector using integer coordinates.
///
/// This struct represents a point or direction in 2D space with integer x and y components.
/// It is optimized for grid-based operations, tile coordinates, pixel positions, and
/// discrete mathematical calculations where floating-point precision is not needed.
///
/// ## Use Cases
///
/// Vector2i is ideal for:
/// - **Tile-based games**: Grid coordinates, map positions, tile indices
/// - **Pixel-perfect graphics**: Screen coordinates, sprite positioning, UI layout
/// - **Grid systems**: Board games, cellular automata, pathfinding on grids
/// - **Discrete mathematics**: Integer-only calculations, counting, indexing
/// - **Performance-critical code**: Avoiding floating-point operations where possible
///
/// ## Limitations
///
/// Unlike Vector2, Vector2i does not support:
/// - Normalization (integer vectors cannot have unit length)
/// - Rotation (requires trigonometric functions)
/// - Interpolation (requires floating-point arithmetic)
/// - Length calculation (would require square root)
///
/// For these operations, convert to Vector2 using `to_vector2()`.
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct Vector2i {
    /// The vector's X component (horizontal coordinate).
    pub x: i32,
    /// The vector's Y component (vertical coordinate).
    pub y: i32,
}

impl Vector2i {
    /// ### Zero vector constant -> Vector2i(0, 0).
    ///
    /// Represents the additive identity for vector operations and the origin point in 2D space.
    /// This vector has zero magnitude and no defined direction.
    pub const ZERO: Vector2i = Vector2i { x: 0, y: 0 };

    /// ### Unit vector constant -> Vector2i(1, 1).
    ///
    /// A vector with both components set to 1, commonly used for uniform scaling
    /// and as a reference vector in grid-based calculations.
    pub const ONE: Vector2i = Vector2i { x: 1, y: 1 };

    /// ### Left-pointing unit vector constant -> Vector2i(-1, 0).
    ///
    /// Points in the negative X direction with unit magnitude. Represents the standard
    /// leftward direction in grid-based coordinate systems.
    pub const LEFT: Vector2i = Vector2i { x: -1, y: 0 };

    /// ### Right-pointing unit vector constant -> Vector2i(1, 0).
    ///
    /// Points in the positive X direction with unit magnitude. Represents the standard
    /// rightward direction and is the basis vector for the X-axis.
    pub const RIGHT: Vector2i = Vector2i { x: 1, y: 0 };

    /// ### Upward-pointing unit vector constant -> Vector2i(0, -1).
    ///
    /// Points in the negative Y direction with unit magnitude. In screen coordinates
    /// where Y increases downward, this represents the upward direction.
    pub const UP: Vector2i = Vector2i { x: 0, y: -1 };

    /// ### Downward-pointing unit vector constant -> Vector2i(0, 1).
    ///
    /// Points in the positive Y direction with unit magnitude. In screen coordinates
    /// where Y increases downward, this represents the downward direction.
    pub const DOWN: Vector2i = Vector2i { x: 0, y: 1 };

    /// ### Creates a new Vector2i with the specified x and y components.
    ///
    /// This is the primary constructor for creating integer vectors with explicit coordinate values.
    /// Both components can be any 32-bit signed integer value, including negative numbers and zero.
    ///
    /// # Arguments
    /// * `x` - The horizontal component (X-coordinate)
    /// * `y` - The vertical component (Y-coordinate)
    ///
    /// # Returns
    /// A new Vector2i instance with the specified components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let tile_pos = Vector2i::new(5, 3);
    /// assert_eq!(tile_pos.x, 5);
    /// assert_eq!(tile_pos.y, 3);
    ///
    /// // Negative coordinates for relative positions
    /// let offset = Vector2i::new(-2, 1);
    /// assert_eq!(offset.x, -2);
    /// assert_eq!(offset.y, 1);
    /// ```
    #[inline]
    pub const fn new(x: i32, y: i32) -> Self {
        Vector2i { x, y }
    }

    /// ### Calculates the squared length of the vector.
    ///
    /// This is the only length-related operation available for integer vectors,
    /// as the actual length would require floating-point square root calculation.
    /// Useful for distance comparisons and magnitude-based operations.
    ///
    /// # Returns
    /// The squared length of the vector (x² + y²).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let v = Vector2i::new(3, 4);
    /// assert_eq!(v.length_squared(), 25); // 3² + 4² = 9 + 16 = 25
    ///
    /// // Useful for distance comparisons without square root
    /// let pos1 = Vector2i::new(0, 0);
    /// let pos2 = Vector2i::new(3, 4);
    /// let distance_sq = (pos2 - pos1).length_squared();
    /// assert_eq!(distance_sq, 25);
    /// ```
    #[inline]
    pub fn length_squared(self) -> i32 {
        self.x * self.x + self.y * self.y
    }

    /// ### Calculates the dot product with another vector.
    ///
    /// The dot product is useful for determining the relationship between two vectors,
    /// such as whether they point in similar directions or are perpendicular.
    ///
    /// # Arguments
    /// * `other` - The other vector to compute the dot product with
    ///
    /// # Returns
    /// The dot product as a 32-bit signed integer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let v1 = Vector2i::new(2, 3);
    /// let v2 = Vector2i::new(4, 1);
    /// assert_eq!(v1.dot(v2), 11); // 2*4 + 3*1 = 8 + 3 = 11
    ///
    /// // Perpendicular vectors have zero dot product
    /// let right = Vector2i::RIGHT;
    /// let up = Vector2i::UP;
    /// assert_eq!(right.dot(up), 0);
    /// ```
    #[inline]
    pub fn dot(self, other: Vector2i) -> i32 {
        self.x * other.x + self.y * other.y
    }

    /// ### Calculates the cross product with another vector.
    ///
    /// In 2D, the cross product returns a scalar representing the Z component
    /// of the 3D cross product if the vectors were extended to 3D space.
    /// Useful for determining relative orientation and signed area calculations.
    ///
    /// # Arguments
    /// * `other` - The other vector to compute the cross product with
    ///
    /// # Returns
    /// The cross product as a 32-bit signed integer.
    /// Positive values indicate counter-clockwise rotation from self to other.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let v1 = Vector2i::new(2, 1);
    /// let v2 = Vector2i::new(1, 3);
    /// assert_eq!(v1.cross(v2), 5); // 2*3 - 1*1 = 6 - 1 = 5
    ///
    /// // Cross product with basis vectors
    /// let right = Vector2i::RIGHT;
    /// let down = Vector2i::DOWN;
    /// assert_eq!(right.cross(down), 1); // 90° counter-clockwise
    /// ```
    #[inline]
    pub fn cross(self, other: Vector2i) -> i32 {
        self.x * other.y - self.y * other.x
    }

    /// ### Converts this integer vector to a floating-point Vector2.
    ///
    /// This conversion allows access to floating-point operations like normalization,
    /// rotation, and interpolation that are not available for integer vectors.
    ///
    /// # Returns
    /// A new Vector2 with the same component values as floating-point numbers.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector2i, Vector2};
    /// let int_vec = Vector2i::new(3, 4);
    /// let float_vec = int_vec.to_vector2();
    /// assert_eq!(float_vec, Vector2::new(3.0, 4.0));
    ///
    /// // Now can use floating-point operations
    /// let length = float_vec.length();
    /// let normalized = float_vec.normalized();
    /// ```
    #[inline]
    pub fn to_vector2(self) -> Vector2 {
        Vector2::new(self.x as f32, self.y as f32)
    }
    /// ### Returns a vector with the absolute values of the components.
    ///
    /// Creates a new vector where both x and y components are their absolute values.
    /// This is useful for distance calculations and ensuring positive coordinates
    /// in grid-based systems.
    ///
    /// # Returns
    /// A new Vector2i with absolute values of both components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let v = Vector2i::new(-3, -4);
    /// let abs_v = v.abs();
    /// assert_eq!(abs_v, Vector2i::new(3, 4));
    ///
    /// // Useful for grid distance calculations
    /// let pos1 = Vector2i::new(2, 5);
    /// let pos2 = Vector2i::new(7, 1);
    /// let distance_vec = (pos2 - pos1).abs();
    /// assert_eq!(distance_vec, Vector2i::new(5, 4)); // Manhattan distance components
    /// ```
    #[inline]
    pub fn abs(self) -> Vector2i {
        Vector2i::new(self.x.abs(), self.y.abs())
    }

    /// ### Returns a vector with the signs of the components.
    ///
    /// Each component becomes -1 if negative, 0 if zero, or 1 if positive.
    /// This is useful for determining direction and creating unit direction vectors.
    ///
    /// # Returns
    /// A new Vector2i with sign values (-1, 0, or 1) for each component.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let v = Vector2i::new(-5, 3);
    /// let sign_v = v.sign();
    /// assert_eq!(sign_v, Vector2i::new(-1, 1));
    ///
    /// let zero_v = Vector2i::new(0, -7);
    /// let sign_zero = zero_v.sign();
    /// assert_eq!(sign_zero, Vector2i::new(0, -1));
    /// ```
    #[inline]
    pub fn sign(self) -> Vector2i {
        Vector2i::new(self.x.signum(), self.y.signum())
    }

    /// ### Returns the component-wise minimum of this vector and another vector.
    ///
    /// Creates a new vector where each component is the minimum of the corresponding
    /// components from both vectors. Useful for bounding box calculations.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// A new Vector2i with the minimum components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let v1 = Vector2i::new(1, 5);
    /// let v2 = Vector2i::new(3, 2);
    /// let min_v = v1.min(v2);
    /// assert_eq!(min_v, Vector2i::new(1, 2));
    ///
    /// // Useful for bounding box calculations
    /// let top_left = Vector2i::new(10, 10);
    /// let bottom_right = Vector2i::new(5, 15);
    /// let actual_top_left = top_left.min(bottom_right);
    /// assert_eq!(actual_top_left, Vector2i::new(5, 10));
    /// ```
    #[inline]
    pub fn min(self, other: Vector2i) -> Vector2i {
        Vector2i::new(self.x.min(other.x), self.y.min(other.y))
    }

    /// ### Returns the component-wise maximum of this vector and another vector.
    ///
    /// Creates a new vector where each component is the maximum of the corresponding
    /// components from both vectors. Useful for bounding box calculations.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// A new Vector2i with the maximum components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let v1 = Vector2i::new(1, 5);
    /// let v2 = Vector2i::new(3, 2);
    /// let max_v = v1.max(v2);
    /// assert_eq!(max_v, Vector2i::new(3, 5));
    ///
    /// // Useful for bounding box calculations
    /// let top_left = Vector2i::new(10, 10);
    /// let bottom_right = Vector2i::new(5, 15);
    /// let actual_bottom_right = top_left.max(bottom_right);
    /// assert_eq!(actual_bottom_right, Vector2i::new(10, 15));
    /// ```
    #[inline]
    pub fn max(self, other: Vector2i) -> Vector2i {
        Vector2i::new(self.x.max(other.x), self.y.max(other.y))
    }

    /// ### Clamps the vector components between minimum and maximum vectors.
    ///
    /// Each component is clamped independently between the corresponding components
    /// of the min and max vectors. Useful for constraining positions within bounds.
    ///
    /// # Arguments
    /// * `min` - The minimum vector (component-wise lower bounds)
    /// * `max` - The maximum vector (component-wise upper bounds)
    ///
    /// # Returns
    /// A new Vector2i with components clamped between min and max.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let pos = Vector2i::new(-5, 15);
    /// let min_bounds = Vector2i::new(0, 0);
    /// let max_bounds = Vector2i::new(10, 10);
    /// let clamped = pos.clamp(min_bounds, max_bounds);
    /// assert_eq!(clamped, Vector2i::new(0, 10));
    ///
    /// // Useful for keeping entities within game world bounds
    /// let player_pos = Vector2i::new(25, -3);
    /// let world_min = Vector2i::new(0, 0);
    /// let world_max = Vector2i::new(20, 20);
    /// let valid_pos = player_pos.clamp(world_min, world_max);
    /// assert_eq!(valid_pos, Vector2i::new(20, 0));
    /// ```
    #[inline]
    pub fn clamp(self, min: Vector2i, max: Vector2i) -> Vector2i {
        Vector2i::new(self.x.clamp(min.x, max.x), self.y.clamp(min.y, max.y))
    }
    /// ### Snaps the vector components to a grid defined by the step vector.
    ///
    /// Each component is rounded to the nearest multiple of the corresponding
    /// component in the step vector. This is particularly useful for tile-based
    /// games and grid alignment operations.
    ///
    /// # Arguments
    /// * `step` - The step vector defining the grid spacing
    ///
    /// # Returns
    /// A new Vector2i with components snapped to the grid.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let pos = Vector2i::new(37, 82);
    /// let tile_size = Vector2i::new(16, 16);
    /// let snapped = pos.snapped(tile_size);
    /// assert_eq!(snapped, Vector2i::new(32, 80)); // Nearest 16x16 grid
    ///
    /// // Useful for tile-based positioning
    /// let pixel_pos = Vector2i::new(123, 67);
    /// let grid_step = Vector2i::new(32, 32);
    /// let tile_pos = pixel_pos.snapped(grid_step);
    /// assert_eq!(tile_pos, Vector2i::new(128, 64));
    /// ```
    #[inline]
    pub fn snapped(self, step: Vector2i) -> Vector2i {
        Vector2i::new(
            if step.x != 0 {
                ((self.x as f32 / step.x as f32).round() as i32) * step.x
            } else {
                self.x
            },
            if step.y != 0 {
                ((self.y as f32 / step.y as f32).round() as i32) * step.y
            } else {
                self.y
            },
        )
    }
    /// ### Checks if this vector is exactly equal to another vector.
    ///
    /// For integer vectors, exact equality is meaningful unlike floating-point vectors.
    /// This method is provided for API consistency with Vector2, though direct
    /// comparison with `==` is equivalent and more idiomatic for integer types.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// `true` if the vectors are exactly equal, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let v1 = Vector2i::new(5, 3);
    /// let v2 = Vector2i::new(5, 3);
    /// let v3 = Vector2i::new(5, 4);
    ///
    /// assert!(v1.is_equal_approx(v2));
    /// assert!(!v1.is_equal_approx(v3));
    ///
    /// // Equivalent to direct comparison for integers
    /// assert_eq!(v1 == v2, v1.is_equal_approx(v2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Vector2i) -> bool {
        self.x == other.x && self.y == other.y
    }

    /// ### Checks if the vector is exactly zero.
    ///
    /// For integer vectors, zero checking is exact unlike floating-point vectors.
    /// This method is provided for API consistency with Vector2.
    ///
    /// # Returns
    /// `true` if both components are exactly zero, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let zero = Vector2i::ZERO;
    /// assert!(zero.is_zero_approx());
    ///
    /// let not_zero = Vector2i::new(1, 0);
    /// assert!(!not_zero.is_zero_approx());
    ///
    /// // Equivalent to direct comparison with ZERO
    /// assert_eq!(zero == Vector2i::ZERO, zero.is_zero_approx());
    /// ```
    #[inline]
    pub fn is_zero_approx(self) -> bool {
        self.x == 0 && self.y == 0
    }

    /// ### Calculates the Manhattan distance to another vector.
    ///
    /// The Manhattan distance (also known as taxicab distance) is the sum of the
    /// absolute differences of the coordinates. This is often more useful than
    /// Euclidean distance for grid-based games and pathfinding.
    ///
    /// # Arguments
    /// * `other` - The other vector to calculate distance to
    ///
    /// # Returns
    /// The Manhattan distance as a positive integer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let pos1 = Vector2i::new(2, 3);
    /// let pos2 = Vector2i::new(5, 7);
    /// let distance = pos1.manhattan_distance(pos2);
    /// assert_eq!(distance, 7); // |5-2| + |7-3| = 3 + 4 = 7
    ///
    /// // Useful for grid-based pathfinding
    /// let start = Vector2i::new(0, 0);
    /// let goal = Vector2i::new(3, 4);
    /// let min_moves = start.manhattan_distance(goal);
    /// assert_eq!(min_moves, 7); // Minimum moves in grid
    /// ```
    #[inline]
    pub fn manhattan_distance(self, other: Vector2i) -> i32 {
        (self.x - other.x).abs() + (self.y - other.y).abs()
    }

    /// ### Calculates the Chebyshev distance to another vector.
    ///
    /// The Chebyshev distance (also known as chessboard distance) is the maximum
    /// of the absolute differences of the coordinates. This represents the minimum
    /// number of moves needed to reach one point from another if diagonal moves are allowed.
    ///
    /// # Arguments
    /// * `other` - The other vector to calculate distance to
    ///
    /// # Returns
    /// The Chebyshev distance as a positive integer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector2i;
    /// let pos1 = Vector2i::new(2, 3);
    /// let pos2 = Vector2i::new(5, 7);
    /// let distance = pos1.chebyshev_distance(pos2);
    /// assert_eq!(distance, 4); // max(|5-2|, |7-3|) = max(3, 4) = 4
    ///
    /// // Useful for chess-like movement or 8-directional pathfinding
    /// let king_pos = Vector2i::new(4, 4);
    /// let target = Vector2i::new(7, 6);
    /// let moves_needed = king_pos.chebyshev_distance(target);
    /// assert_eq!(moves_needed, 3); // King can move diagonally
    /// ```
    #[inline]
    pub fn chebyshev_distance(self, other: Vector2i) -> i32 {
        (self.x - other.x).abs().max((self.y - other.y).abs())
    }
}

impl fmt::Display for Vector2i {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "({}, {})", self.x, self.y)
    }
}
