//! Vector2 implementation providing feature parity with <PERSON><PERSON>'s Vector2 class.
//!
//! This module implements a 2D vector with all the mathematical operations,
//! utility functions, and transformations available in <PERSON><PERSON>'s Vector2 class.

use std::fmt;
use std::ops::{Add, AddAssign, Sub, <PERSON><PERSON>sign, <PERSON><PERSON>, <PERSON><PERSON><PERSON>sign, Div, <PERSON>v<PERSON>sign, Neg, Index, IndexMut};

/// A 2D vector using floating-point coordinates.
///
/// This struct provides feature parity with <PERSON><PERSON>'s Vector2 class, including
/// all mathematical operations, interpolation methods, and utility functions.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct Vector2 {
    /// The vector's X component.
    pub x: f32,
    /// The vector's Y component.
    pub y: f32,
}

/// Axis constants for Vector2 operations.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum Axis {
    /// X axis (horizontal).
    X = 0,
    /// Y axis (vertical).
    Y = 1,
}

impl Vector2 {
    /// Zero vector constant (0, 0).
    pub const ZERO: Vector2 = Vector2 { x: 0.0, y: 0.0 };

    /// One vector constant (1, 1).
    pub const ONE: Vector2 = Vector2 { x: 1.0, y: 1.0 };

    /// Infinity vector constant (∞, ∞).
    pub const INF: Vector2 = Vector2 { x: f32::INFINITY, y: f32::INFINITY };

    /// Left vector constant (-1, 0).
    pub const LEFT: Vector2 = Vector2 { x: -1.0, y: 0.0 };

    /// Right vector constant (1, 0).
    pub const RIGHT: Vector2 = Vector2 { x: 1.0, y: 0.0 };

    /// Up vector constant (0, -1). Note: Y-axis points down in Godot's coordinate system.
    pub const UP: Vector2 = Vector2 { x: 0.0, y: -1.0 };

    /// Down vector constant (0, 1). Note: Y-axis points down in Godot's coordinate system.
    pub const DOWN: Vector2 = Vector2 { x: 0.0, y: 1.0 };

    /// Creates a new Vector2 with the given x and y components.
    ///
    /// # Arguments
    /// * `x` - The X component
    /// * `y` - The Y component
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(3.0, 4.0);
    /// assert_eq!(v.x, 3.0);
    /// assert_eq!(v.y, 4.0);
    /// ```
    #[inline]
    pub const fn new(x: f32, y: f32) -> Self {
        Vector2 { x, y }
    }

    /// Creates a unit Vector2 rotated to the given angle in radians.
    ///
    /// This is equivalent to doing `Vector2(cos(angle), sin(angle))` or `Vector2::RIGHT.rotated(angle)`.
    ///
    /// # Arguments
    /// * `angle` - The angle in radians
    ///
    /// # Examples
    /// ```
    /// use std::f32::consts::PI;
    /// let v = Vector2::from_angle(0.0);
    /// assert!((v.x - 1.0).abs() < f32::EPSILON);
    /// assert!(v.y.abs() < f32::EPSILON);
    ///
    /// let v = Vector2::from_angle(PI / 2.0);
    /// assert!(v.x.abs() < f32::EPSILON);
    /// assert!((v.y - 1.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn from_angle(angle: f32) -> Self {
        Vector2::new(angle.cos(), angle.sin())
    }

    /// Returns the angle this vector makes with the positive X-axis in radians.
    ///
    /// The angle is in the range [-π, π]. For example, `Vector2::RIGHT.angle()` returns 0,
    /// and `Vector2::DOWN.angle()` returns π/2.
    ///
    /// # Examples
    /// ```
    /// use std::f32::consts::PI;
    /// assert!((Vector2::RIGHT.angle() - 0.0).abs() < f32::EPSILON);
    /// assert!((Vector2::DOWN.angle() - PI / 2.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn angle(&self) -> f32 {
        self.y.atan2(self.x)
    }

    /// Returns the angle between this vector and the `to` vector in radians.
    ///
    /// The angle is always positive and in the range [0, π].
    ///
    /// # Arguments
    /// * `to` - The target vector
    ///
    /// # Examples
    /// ```
    /// use std::f32::consts::PI;
    /// let angle = Vector2::RIGHT.angle_to(Vector2::UP);
    /// assert!((angle - PI / 2.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn angle_to(&self, to: Vector2) -> f32 {
        self.cross(to).atan2(self.dot(to))
    }

    /// Returns the angle from this vector to the point `to` in radians.
    ///
    /// This is equivalent to `(to - self).angle()`.
    ///
    /// # Arguments
    /// * `to` - The target point
    ///
    /// # Examples
    /// ```
    /// let angle = Vector2::ZERO.angle_to_point(Vector2::new(1.0, 1.0));
    /// assert!((angle - std::f32::consts::PI / 4.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn angle_to_point(&self, to: Vector2) -> f32 {
        (to - *self).angle()
    }

    /// Returns a new vector with all components as their absolute values.
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(-3.0, -4.0);
    /// let abs_v = v.abs();
    /// assert_eq!(abs_v, Vector2::new(3.0, 4.0));
    /// ```
    #[inline]
    pub fn abs(&self) -> Vector2 {
        Vector2::new(self.x.abs(), self.y.abs())
    }

    /// Returns the aspect ratio of this vector (width / height).
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(4.0, 2.0);
    /// assert_eq!(v.aspect(), 2.0);
    /// ```
    #[inline]
    pub fn aspect(&self) -> f32 {
        self.x / self.y
    }

    /// Returns the 2D analog of the cross product for this vector and `with`.
    ///
    /// This is the signed area of the parallelogram formed by the two vectors.
    /// If the second vector is clockwise from the first vector, then the cross product
    /// is the positive area. If counter-clockwise, the cross product is the negative area.
    /// If the two vectors are parallel this returns zero.
    ///
    /// # Arguments
    /// * `with` - The other vector
    ///
    /// # Examples
    /// ```
    /// let cross = Vector2::RIGHT.cross(Vector2::UP);
    /// assert!(cross > 0.0); // RIGHT to UP is counter-clockwise
    /// ```
    #[inline]
    pub fn cross(&self, with: Vector2) -> f32 {
        self.x * with.y - self.y * with.x
    }

    /// Returns the dot product of this vector and `with`.
    ///
    /// This can be used to compare the angle between two vectors. The dot product
    /// will be 0 for a right angle (90 degrees), greater than 0 for angles narrower
    /// than 90 degrees and lower than 0 for angles wider than 90 degrees.
    ///
    /// When using unit (normalized) vectors, the result will always be between
    /// -1.0 (180 degree angle) and 1.0 (0 degree angle).
    ///
    /// # Arguments
    /// * `with` - The other vector
    ///
    /// # Examples
    /// ```
    /// let dot = Vector2::RIGHT.dot(Vector2::RIGHT);
    /// assert_eq!(dot, 1.0);
    ///
    /// let dot = Vector2::RIGHT.dot(Vector2::UP);
    /// assert_eq!(dot, 0.0);
    /// ```
    #[inline]
    pub fn dot(&self, with: Vector2) -> f32 {
        self.x * with.x + self.y * with.y
    }

    /// Returns the length (magnitude) of this vector.
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(3.0, 4.0);
    /// assert_eq!(v.length(), 5.0);
    /// ```
    #[inline]
    pub fn length(&self) -> f32 {
        (self.x * self.x + self.y * self.y).sqrt()
    }

    /// Returns the squared length (squared magnitude) of this vector.
    ///
    /// This method runs faster than `length()`, so prefer it if you need to compare
    /// vectors or need the squared distance for some formula.
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(3.0, 4.0);
    /// assert_eq!(v.length_squared(), 25.0);
    /// ```
    #[inline]
    pub fn length_squared(&self) -> f32 {
        self.x * self.x + self.y * self.y
    }

    /// Returns the result of scaling the vector to unit length.
    ///
    /// Equivalent to `v / v.length()`. Returns `(0, 0)` if `v.length() == 0`.
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(3.0, 4.0);
    /// let normalized = v.normalized();
    /// assert!((normalized.length() - 1.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn normalized(&self) -> Vector2 {
        let length = self.length();
        if length == 0.0 {
            Vector2::ZERO
        } else {
            *self / length
        }
    }

    /// Returns the distance between this vector and `to`.
    ///
    /// # Arguments
    /// * `to` - The target vector
    ///
    /// # Examples
    /// ```
    /// let distance = Vector2::ZERO.distance_to(Vector2::new(3.0, 4.0));
    /// assert_eq!(distance, 5.0);
    /// ```
    #[inline]
    pub fn distance_to(&self, to: Vector2) -> f32 {
        (*self - to).length()
    }

    /// Returns the squared distance between this vector and `to`.
    ///
    /// This method runs faster than `distance_to()`, so prefer it if you need to
    /// compare vectors or need the squared distance for some formula.
    ///
    /// # Arguments
    /// * `to` - The target vector
    ///
    /// # Examples
    /// ```
    /// let distance_sq = Vector2::ZERO.distance_squared_to(Vector2::new(3.0, 4.0));
    /// assert_eq!(distance_sq, 25.0);
    /// ```
    #[inline]
    pub fn distance_squared_to(&self, to: Vector2) -> f32 {
        (*self - to).length_squared()
    }

    /// Returns the normalized vector pointing from this vector to `to`.
    ///
    /// This is equivalent to using `(b - a).normalized()`.
    ///
    /// # Arguments
    /// * `to` - The target vector
    ///
    /// # Examples
    /// ```
    /// let direction = Vector2::ZERO.direction_to(Vector2::new(1.0, 0.0));
    /// assert_eq!(direction, Vector2::RIGHT);
    /// ```
    #[inline]
    pub fn direction_to(&self, to: Vector2) -> Vector2 {
        (to - *self).normalized()
    }

    /// Returns a perpendicular vector rotated 90 degrees counter-clockwise compared to the original.
    ///
    /// The returned vector has the same length as the original.
    ///
    /// # Examples
    /// ```
    /// let ortho = Vector2::RIGHT.orthogonal();
    /// assert_eq!(ortho, Vector2::UP);
    /// ```
    #[inline]
    pub fn orthogonal(&self) -> Vector2 {
        Vector2::new(-self.y, self.x)
    }

    /// Returns the result of rotating this vector by `angle` (in radians).
    ///
    /// # Arguments
    /// * `angle` - The rotation angle in radians
    ///
    /// # Examples
    /// ```
    /// use std::f32::consts::PI;
    /// let rotated = Vector2::RIGHT.rotated(PI / 2.0);
    /// assert!((rotated.x - 0.0).abs() < f32::EPSILON);
    /// assert!((rotated.y - 1.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn rotated(&self, angle: f32) -> Vector2 {
        let cos_a = angle.cos();
        let sin_a = angle.sin();
        Vector2::new(
            self.x * cos_a - self.y * sin_a,
            self.x * sin_a + self.y * cos_a,
        )
    }

    /// Returns the result of the linear interpolation between this vector and `to` by amount `weight`.
    ///
    /// `weight` is on the range of 0.0 to 1.0, representing the amount of interpolation.
    ///
    /// # Arguments
    /// * `to` - The target vector
    /// * `weight` - The interpolation weight (0.0 to 1.0)
    ///
    /// # Examples
    /// ```
    /// let a = Vector2::new(0.0, 0.0);
    /// let b = Vector2::new(10.0, 10.0);
    /// let lerped = a.lerp(b, 0.5);
    /// assert_eq!(lerped, Vector2::new(5.0, 5.0));
    /// ```
    #[inline]
    pub fn lerp(&self, to: Vector2, weight: f32) -> Vector2 {
        *self + (to - *self) * weight
    }

    /// Returns the result of spherical linear interpolation between this vector and `to`.
    ///
    /// This method also handles interpolating the lengths if the input vectors have different lengths.
    /// For the special case of one or both input vectors having zero length, this method behaves like `lerp()`.
    ///
    /// # Arguments
    /// * `to` - The target vector
    /// * `weight` - The interpolation weight (0.0 to 1.0)
    ///
    /// # Examples
    /// ```
    /// let a = Vector2::RIGHT;
    /// let b = Vector2::UP;
    /// let slerped = a.slerp(b, 0.5);
    /// // Result should be a vector at 45 degrees
    /// ```
    #[inline]
    pub fn slerp(&self, to: Vector2, weight: f32) -> Vector2 {
        let start_length_sq = self.length_squared();
        let end_length_sq = to.length_squared();

        if start_length_sq == 0.0 || end_length_sq == 0.0 {
            // Fallback to linear interpolation for zero-length vectors
            return self.lerp(to, weight);
        }

        let start_length = start_length_sq.sqrt();
        let end_length = end_length_sq.sqrt();
        let result_length = start_length + (end_length - start_length) * weight;

        let angle = self.angle_to(to) * weight;
        self.rotated(angle) * (result_length / start_length)
    }

    /// Performs a cubic interpolation between this vector and `b` using `pre_a` and `post_b` as handles.
    ///
    /// # Arguments
    /// * `b` - The target vector
    /// * `pre_a` - The control point before this vector
    /// * `post_b` - The control point after the target vector
    /// * `weight` - The interpolation weight (0.0 to 1.0)
    ///
    /// # Examples
    /// ```
    /// let a = Vector2::new(0.0, 0.0);
    /// let b = Vector2::new(10.0, 0.0);
    /// let pre_a = Vector2::new(-5.0, 0.0);
    /// let post_b = Vector2::new(15.0, 0.0);
    /// let cubic = a.cubic_interpolate(b, pre_a, post_b, 0.5);
    /// ```
    #[inline]
    pub fn cubic_interpolate(&self, b: Vector2, pre_a: Vector2, post_b: Vector2, weight: f32) -> Vector2 {
        let weight2 = weight * weight;
        let weight3 = weight2 * weight;

        *self * (2.0 * weight3 - 3.0 * weight2 + 1.0)
            + b * (-2.0 * weight3 + 3.0 * weight2)
            + pre_a * (weight3 - 2.0 * weight2 + weight)
            + post_b * (weight3 - weight2)
    }

    /// Performs a cubic interpolation between this vector and `b` using `pre_a` and `post_b` as handles,
    /// with time values for smoother interpolation.
    ///
    /// # Arguments
    /// * `b` - The target vector
    /// * `pre_a` - The control point before this vector
    /// * `post_b` - The control point after the target vector
    /// * `weight` - The interpolation weight (0.0 to 1.0)
    /// * `b_t` - Time value for point b
    /// * `pre_a_t` - Time value for pre_a
    /// * `post_b_t` - Time value for post_b
    #[inline]
    pub fn cubic_interpolate_in_time(
        &self,
        b: Vector2,
        pre_a: Vector2,
        post_b: Vector2,
        weight: f32,
        _b_t: f32,
        _pre_a_t: f32,
        _post_b_t: f32,
    ) -> Vector2 {
        // Simplified implementation - in a full implementation, this would use
        // the time values to create a more sophisticated interpolation
        self.cubic_interpolate(b, pre_a, post_b, weight)
    }

    /// Returns the point at the given `t` on the Bézier curve defined by this vector
    /// and the given control points.
    ///
    /// # Arguments
    /// * `control_1` - First control point
    /// * `control_2` - Second control point
    /// * `end` - End point
    /// * `t` - Parameter value (0.0 to 1.0)
    ///
    /// # Examples
    /// ```
    /// let start = Vector2::new(0.0, 0.0);
    /// let control1 = Vector2::new(0.0, 10.0);
    /// let control2 = Vector2::new(10.0, 10.0);
    /// let end = Vector2::new(10.0, 0.0);
    /// let point = start.bezier_interpolate(control1, control2, end, 0.5);
    /// ```
    #[inline]
    pub fn bezier_interpolate(&self, control_1: Vector2, control_2: Vector2, end: Vector2, t: f32) -> Vector2 {
        let omt = 1.0 - t;
        let omt2 = omt * omt;
        let omt3 = omt2 * omt;
        let t2 = t * t;
        let t3 = t2 * t;

        *self * omt3 + control_1 * (omt2 * t * 3.0) + control_2 * (omt * t2 * 3.0) + end * t3
    }

    /// Returns the derivative at the given `t` on the Bézier curve defined by this vector
    /// and the given control points.
    ///
    /// # Arguments
    /// * `control_1` - First control point
    /// * `control_2` - Second control point
    /// * `end` - End point
    /// * `t` - Parameter value (0.0 to 1.0)
    #[inline]
    pub fn bezier_derivative(&self, control_1: Vector2, control_2: Vector2, end: Vector2, t: f32) -> Vector2 {
        let omt = 1.0 - t;
        let omt2 = omt * omt;
        let t2 = t * t;

        (control_1 - *self) * (3.0 * omt2) + (control_2 - control_1) * (6.0 * omt * t) + (end - control_2) * (3.0 * t2)
    }

    /// Returns a new vector moved toward `to` by the fixed `delta` amount.
    ///
    /// Will not go past the final value.
    ///
    /// # Arguments
    /// * `to` - The target vector
    /// * `delta` - The maximum distance to move
    ///
    /// # Examples
    /// ```
    /// let moved = Vector2::ZERO.move_toward(Vector2::new(10.0, 0.0), 5.0);
    /// assert_eq!(moved, Vector2::new(5.0, 0.0));
    /// ```
    #[inline]
    pub fn move_toward(&self, to: Vector2, delta: f32) -> Vector2 {
        let diff = to - *self;
        let length = diff.length();
        if length <= delta || length == 0.0 {
            to
        } else {
            *self + diff / length * delta
        }
    }

    /// Returns the vector "bounced off" from a line defined by the given normal `n`.
    ///
    /// Note: `bounce()` performs the operation that most engines and frameworks call `reflect()`.
    ///
    /// # Arguments
    /// * `n` - The normal vector (should be normalized)
    ///
    /// # Examples
    /// ```
    /// let incident = Vector2::new(1.0, -1.0);
    /// let normal = Vector2::UP;
    /// let bounced = incident.bounce(normal);
    /// assert_eq!(bounced, Vector2::new(1.0, 1.0));
    /// ```
    #[inline]
    pub fn bounce(&self, n: Vector2) -> Vector2 {
        -self.reflect(n)
    }

    /// Returns the result of reflecting the vector from a line defined by the given direction vector `line`.
    ///
    /// Note: `reflect()` differs from what other engines call `reflect()`. In Godot, you specify
    /// the direction of the line directly. See also `bounce()` which does what most engines call `reflect()`.
    ///
    /// # Arguments
    /// * `line` - The line direction vector
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.0, 1.0);
    /// let line = Vector2::RIGHT;
    /// let reflected = v.reflect(line);
    /// ```
    #[inline]
    pub fn reflect(&self, line: Vector2) -> Vector2 {
        let n = line.orthogonal().normalized();
        *self - n * 2.0 * self.dot(n)
    }

    /// Returns a new vector resulting from sliding this vector along a line with normal `n`.
    ///
    /// The resulting new vector is perpendicular to `n`, and is equivalent to this vector
    /// minus its projection on `n`.
    ///
    /// # Arguments
    /// * `n` - The normal vector (must be normalized)
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.0, 1.0);
    /// let normal = Vector2::UP;
    /// let slid = v.slide(normal);
    /// assert_eq!(slid, Vector2::new(1.0, 0.0));
    /// ```
    #[inline]
    pub fn slide(&self, n: Vector2) -> Vector2 {
        *self - n * self.dot(n)
    }

    /// Returns a new vector resulting from projecting this vector onto the given vector `b`.
    ///
    /// The resulting new vector is parallel to `b`.
    ///
    /// # Arguments
    /// * `b` - The vector to project onto
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.0, 1.0);
    /// let onto = Vector2::RIGHT;
    /// let projected = v.project(onto);
    /// assert_eq!(projected, Vector2::new(1.0, 0.0));
    /// ```
    #[inline]
    pub fn project(&self, b: Vector2) -> Vector2 {
        let b_length_sq = b.length_squared();
        if b_length_sq == 0.0 {
            Vector2::new(f32::NAN, f32::NAN)
        } else {
            b * (self.dot(b) / b_length_sq)
        }
    }

    /// Returns a new vector with all components rounded up (towards positive infinity).
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.2, -2.8);
    /// let ceiled = v.ceil();
    /// assert_eq!(ceiled, Vector2::new(2.0, -2.0));
    /// ```
    #[inline]
    pub fn ceil(&self) -> Vector2 {
        Vector2::new(self.x.ceil(), self.y.ceil())
    }

    /// Returns a new vector with all components rounded down (towards negative infinity).
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.8, -2.2);
    /// let floored = v.floor();
    /// assert_eq!(floored, Vector2::new(1.0, -3.0));
    /// ```
    #[inline]
    pub fn floor(&self) -> Vector2 {
        Vector2::new(self.x.floor(), self.y.floor())
    }

    /// Returns a new vector with all components rounded to the nearest integer.
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.6, -2.4);
    /// let rounded = v.round();
    /// assert_eq!(rounded, Vector2::new(2.0, -2.0));
    /// ```
    #[inline]
    pub fn round(&self) -> Vector2 {
        Vector2::new(self.x.round(), self.y.round())
    }

    /// Returns a new vector with each component set to 1.0 if it's positive, -1.0 if it's negative, and 0.0 if it's zero.
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(3.0, -4.0);
    /// let sign = v.sign();
    /// assert_eq!(sign, Vector2::new(1.0, -1.0));
    /// ```
    #[inline]
    pub fn sign(&self) -> Vector2 {
        Vector2::new(
            if self.x > 0.0 { 1.0 } else if self.x < 0.0 { -1.0 } else { 0.0 },
            if self.y > 0.0 { 1.0 } else if self.y < 0.0 { -1.0 } else { 0.0 },
        )
    }

    /// Returns the vector with a maximum length by limiting its length to `length`.
    ///
    /// # Arguments
    /// * `length` - The maximum length (default: 1.0)
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(3.0, 4.0); // length = 5.0
    /// let limited = v.limit_length(2.0);
    /// assert!((limited.length() - 2.0).abs() < f32::EPSILON);
    /// ```
    #[inline]
    pub fn limit_length(&self, length: f32) -> Vector2 {
        let current_length = self.length();
        if current_length > 0.0 && current_length > length {
            *self * (length / current_length)
        } else {
            *self
        }
    }

    /// Returns a new vector with all components clamped between the components of `min` and `max`.
    ///
    /// # Arguments
    /// * `min` - The minimum values
    /// * `max` - The maximum values
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(-1.0, 5.0);
    /// let clamped = v.clamp(Vector2::ZERO, Vector2::new(3.0, 3.0));
    /// assert_eq!(clamped, Vector2::new(0.0, 3.0));
    /// ```
    #[inline]
    pub fn clamp(&self, min: Vector2, max: Vector2) -> Vector2 {
        Vector2::new(
            self.x.clamp(min.x, max.x),
            self.y.clamp(min.y, max.y),
        )
    }

    /// Returns a new vector with all components clamped between `min` and `max`.
    ///
    /// # Arguments
    /// * `min` - The minimum value
    /// * `max` - The maximum value
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(-1.0, 5.0);
    /// let clamped = v.clampf(0.0, 3.0);
    /// assert_eq!(clamped, Vector2::new(0.0, 3.0));
    /// ```
    #[inline]
    pub fn clampf(&self, min: f32, max: f32) -> Vector2 {
        Vector2::new(
            self.x.clamp(min, max),
            self.y.clamp(min, max),
        )
    }

    /// Returns the component-wise minimum of this and `with`.
    ///
    /// # Arguments
    /// * `with` - The other vector
    ///
    /// # Examples
    /// ```
    /// let a = Vector2::new(1.0, 5.0);
    /// let b = Vector2::new(3.0, 2.0);
    /// let min = a.min(b);
    /// assert_eq!(min, Vector2::new(1.0, 2.0));
    /// ```
    #[inline]
    pub fn min(&self, with: Vector2) -> Vector2 {
        Vector2::new(self.x.min(with.x), self.y.min(with.y))
    }

    /// Returns the component-wise minimum of this and `with`.
    ///
    /// # Arguments
    /// * `with` - The scalar value
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.0, 5.0);
    /// let min = v.minf(3.0);
    /// assert_eq!(min, Vector2::new(1.0, 3.0));
    /// ```
    #[inline]
    pub fn minf(&self, with: f32) -> Vector2 {
        Vector2::new(self.x.min(with), self.y.min(with))
    }

    /// Returns the component-wise maximum of this and `with`.
    ///
    /// # Arguments
    /// * `with` - The other vector
    ///
    /// # Examples
    /// ```
    /// let a = Vector2::new(1.0, 5.0);
    /// let b = Vector2::new(3.0, 2.0);
    /// let max = a.max(b);
    /// assert_eq!(max, Vector2::new(3.0, 5.0));
    /// ```
    #[inline]
    pub fn max(&self, with: Vector2) -> Vector2 {
        Vector2::new(self.x.max(with.x), self.y.max(with.y))
    }

    /// Returns the component-wise maximum of this and `with`.
    ///
    /// # Arguments
    /// * `with` - The scalar value
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.0, 5.0);
    /// let max = v.maxf(3.0);
    /// assert_eq!(max, Vector2::new(3.0, 5.0));
    /// ```
    #[inline]
    pub fn maxf(&self, with: f32) -> Vector2 {
        Vector2::new(self.x.max(with), self.y.max(with))
    }

    /// Returns the axis of the vector's highest value.
    ///
    /// If all components are equal, this method returns `Axis::X`.
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.0, 3.0);
    /// assert_eq!(v.max_axis_index(), Axis::Y);
    /// ```
    #[inline]
    pub fn max_axis_index(&self) -> Axis {
        if self.x >= self.y {
            Axis::X
        } else {
            Axis::Y
        }
    }

    /// Returns the axis of the vector's lowest value.
    ///
    /// If all components are equal, this method returns `Axis::Y`.
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.0, 3.0);
    /// assert_eq!(v.min_axis_index(), Axis::X);
    /// ```
    #[inline]
    pub fn min_axis_index(&self) -> Axis {
        if self.x <= self.y {
            Axis::X
        } else {
            Axis::Y
        }
    }

    /// Returns a new vector with each component snapped to the nearest multiple of the corresponding component in `step`.
    ///
    /// # Arguments
    /// * `step` - The step vector
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(3.7, 7.2);
    /// let snapped = v.snapped(Vector2::new(2.0, 5.0));
    /// assert_eq!(snapped, Vector2::new(4.0, 5.0));
    /// ```
    #[inline]
    pub fn snapped(&self, step: Vector2) -> Vector2 {
        Vector2::new(
            if step.x != 0.0 { (self.x / step.x).round() * step.x } else { self.x },
            if step.y != 0.0 { (self.y / step.y).round() * step.y } else { self.y },
        )
    }

    /// Returns a new vector with each component snapped to the nearest multiple of `step`.
    ///
    /// # Arguments
    /// * `step` - The step value
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(3.7, 7.2);
    /// let snapped = v.snappedf(2.0);
    /// assert_eq!(snapped, Vector2::new(4.0, 8.0));
    /// ```
    #[inline]
    pub fn snappedf(&self, step: f32) -> Vector2 {
        if step != 0.0 {
            Vector2::new(
                (self.x / step).round() * step,
                (self.y / step).round() * step,
            )
        } else {
            *self
        }
    }

    /// Returns a vector composed of the positive modulo of this vector's components and `modv`'s components.
    ///
    /// # Arguments
    /// * `modv` - The modulo vector
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(7.0, -3.0);
    /// let modded = v.posmodv(Vector2::new(3.0, 2.0));
    /// assert_eq!(modded, Vector2::new(1.0, 1.0));
    /// ```
    #[inline]
    pub fn posmodv(&self, modv: Vector2) -> Vector2 {
        Vector2::new(
            self.x - (self.x / modv.x).floor() * modv.x,
            self.y - (self.y / modv.y).floor() * modv.y,
        )
    }

    /// Returns a vector composed of the positive modulo of this vector's components and `mod_val`.
    ///
    /// # Arguments
    /// * `mod_val` - The modulo value
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(7.0, -3.0);
    /// let modded = v.posmod(3.0);
    /// assert_eq!(modded, Vector2::new(1.0, 0.0));
    /// ```
    #[inline]
    pub fn posmod(&self, mod_val: f32) -> Vector2 {
        Vector2::new(
            self.x - (self.x / mod_val).floor() * mod_val,
            self.y - (self.y / mod_val).floor() * mod_val,
        )
    }

    /// Returns `true` if this vector and `to` are approximately equal.
    ///
    /// # Arguments
    /// * `to` - The vector to compare with
    ///
    /// # Examples
    /// ```
    /// let a = Vector2::new(1.0, 2.0);
    /// let b = Vector2::new(1.0000001, 2.0000001);
    /// assert!(a.is_equal_approx(b));
    /// ```
    #[inline]
    pub fn is_equal_approx(&self, to: Vector2) -> bool {
        const EPSILON: f32 = 1e-4;
        (self.x - to.x).abs() < EPSILON && (self.y - to.y).abs() < EPSILON
    }

    /// Returns `true` if this vector is finite (all components are finite).
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(1.0, 2.0);
    /// assert!(v.is_finite());
    ///
    /// let inf_v = Vector2::new(f32::INFINITY, 2.0);
    /// assert!(!inf_v.is_finite());
    /// ```
    #[inline]
    pub fn is_finite(&self) -> bool {
        self.x.is_finite() && self.y.is_finite()
    }

    /// Returns `true` if the vector is normalized (its length is approximately equal to 1).
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::RIGHT;
    /// assert!(v.is_normalized());
    ///
    /// let v2 = Vector2::new(3.0, 4.0);
    /// assert!(!v2.is_normalized());
    /// assert!(v2.normalized().is_normalized());
    /// ```
    #[inline]
    pub fn is_normalized(&self) -> bool {
        (self.length_squared() - 1.0).abs() < f32::EPSILON
    }

    /// Returns `true` if this vector's values are approximately zero.
    ///
    /// # Examples
    /// ```
    /// let v = Vector2::new(0.0000001, -0.0000001);
    /// assert!(v.is_zero_approx());
    /// ```
    #[inline]
    pub fn is_zero_approx(&self) -> bool {
        const EPSILON: f32 = 1e-4;
        self.x.abs() < EPSILON && self.y.abs() < EPSILON
    }
}

// Default implementation
impl Default for Vector2 {
    #[inline]
    fn default() -> Self {
        Vector2::ZERO
    }
}

// Display implementation
impl fmt::Display for Vector2 {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "({}, {})", self.x, self.y)
    }
}

// Index implementation for accessing components by index
impl Index<usize> for Vector2 {
    type Output = f32;

    #[inline]
    fn index(&self, index: usize) -> &Self::Output {
        match index {
            0 => &self.x,
            1 => &self.y,
            _ => panic!("Index out of bounds for Vector2: {}", index),
        }
    }
}

impl IndexMut<usize> for Vector2 {
    #[inline]
    fn index_mut(&mut self, index: usize) -> &mut Self::Output {
        match index {
            0 => &mut self.x,
            1 => &mut self.y,
            _ => panic!("Index out of bounds for Vector2: {}", index),
        }
    }
}

impl Index<Axis> for Vector2 {
    type Output = f32;

    #[inline]
    fn index(&self, axis: Axis) -> &Self::Output {
        match axis {
            Axis::X => &self.x,
            Axis::Y => &self.y,
        }
    }
}

impl IndexMut<Axis> for Vector2 {
    #[inline]
    fn index_mut(&mut self, axis: Axis) -> &mut Self::Output {
        match axis {
            Axis::X => &mut self.x,
            Axis::Y => &mut self.y,
        }
    }
}

// Arithmetic operations
impl Add for Vector2 {
    type Output = Vector2;

    #[inline]
    fn add(self, rhs: Vector2) -> Self::Output {
        Vector2::new(self.x + rhs.x, self.y + rhs.y)
    }
}

impl AddAssign for Vector2 {
    #[inline]
    fn add_assign(&mut self, rhs: Vector2) {
        self.x += rhs.x;
        self.y += rhs.y;
    }
}

impl Sub for Vector2 {
    type Output = Vector2;

    #[inline]
    fn sub(self, rhs: Vector2) -> Self::Output {
        Vector2::new(self.x - rhs.x, self.y - rhs.y)
    }
}

impl SubAssign for Vector2 {
    #[inline]
    fn sub_assign(&mut self, rhs: Vector2) {
        self.x -= rhs.x;
        self.y -= rhs.y;
    }
}

impl Mul<Vector2> for Vector2 {
    type Output = Vector2;

    #[inline]
    fn mul(self, rhs: Vector2) -> Self::Output {
        Vector2::new(self.x * rhs.x, self.y * rhs.y)
    }
}

impl Mul<f32> for Vector2 {
    type Output = Vector2;

    #[inline]
    fn mul(self, rhs: f32) -> Self::Output {
        Vector2::new(self.x * rhs, self.y * rhs)
    }
}

impl Mul<Vector2> for f32 {
    type Output = Vector2;

    #[inline]
    fn mul(self, rhs: Vector2) -> Self::Output {
        Vector2::new(self * rhs.x, self * rhs.y)
    }
}

impl MulAssign<Vector2> for Vector2 {
    #[inline]
    fn mul_assign(&mut self, rhs: Vector2) {
        self.x *= rhs.x;
        self.y *= rhs.y;
    }
}

impl MulAssign<f32> for Vector2 {
    #[inline]
    fn mul_assign(&mut self, rhs: f32) {
        self.x *= rhs;
        self.y *= rhs;
    }
}

impl Div<Vector2> for Vector2 {
    type Output = Vector2;

    #[inline]
    fn div(self, rhs: Vector2) -> Self::Output {
        Vector2::new(self.x / rhs.x, self.y / rhs.y)
    }
}

impl Div<f32> for Vector2 {
    type Output = Vector2;

    #[inline]
    fn div(self, rhs: f32) -> Self::Output {
        Vector2::new(self.x / rhs, self.y / rhs)
    }
}

impl DivAssign<Vector2> for Vector2 {
    #[inline]
    fn div_assign(&mut self, rhs: Vector2) {
        self.x /= rhs.x;
        self.y /= rhs.y;
    }
}

impl DivAssign<f32> for Vector2 {
    #[inline]
    fn div_assign(&mut self, rhs: f32) {
        self.x /= rhs;
        self.y /= rhs;
    }
}

impl Neg for Vector2 {
    type Output = Vector2;

    #[inline]
    fn neg(self) -> Self::Output {
        Vector2::new(-self.x, -self.y)
    }
}

// Comparison operations
impl PartialOrd for Vector2 {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        match self.x.partial_cmp(&other.x) {
            Some(std::cmp::Ordering::Equal) => self.y.partial_cmp(&other.y),
            other => other,
        }
    }
}

// From/Into implementations for convenience
impl From<(f32, f32)> for Vector2 {
    #[inline]
    fn from((x, y): (f32, f32)) -> Self {
        Vector2::new(x, y)
    }
}

impl From<Vector2> for (f32, f32) {
    #[inline]
    fn from(v: Vector2) -> Self {
        (v.x, v.y)
    }
}

impl From<[f32; 2]> for Vector2 {
    #[inline]
    fn from([x, y]: [f32; 2]) -> Self {
        Vector2::new(x, y)
    }
}

impl From<Vector2> for [f32; 2] {
    #[inline]
    fn from(v: Vector2) -> Self {
        [v.x, v.y]
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::f32::consts::PI;

    #[test]
    fn test_constants() {
        assert_eq!(Vector2::ZERO, Vector2::new(0.0, 0.0));
        assert_eq!(Vector2::ONE, Vector2::new(1.0, 1.0));
        assert_eq!(Vector2::LEFT, Vector2::new(-1.0, 0.0));
        assert_eq!(Vector2::RIGHT, Vector2::new(1.0, 0.0));
        assert_eq!(Vector2::UP, Vector2::new(0.0, -1.0));
        assert_eq!(Vector2::DOWN, Vector2::new(0.0, 1.0));
    }

    #[test]
    fn test_basic_operations() {
        let a = Vector2::new(3.0, 4.0);
        let b = Vector2::new(1.0, 2.0);

        assert_eq!(a + b, Vector2::new(4.0, 6.0));
        assert_eq!(a - b, Vector2::new(2.0, 2.0));
        assert_eq!(a * 2.0, Vector2::new(6.0, 8.0));
        assert_eq!(a / 2.0, Vector2::new(1.5, 2.0));
    }

    #[test]
    fn test_length_and_normalization() {
        let v = Vector2::new(3.0, 4.0);
        assert_eq!(v.length(), 5.0);
        assert_eq!(v.length_squared(), 25.0);

        let normalized = v.normalized();
        assert!((normalized.length() - 1.0).abs() < f32::EPSILON);
        assert!(normalized.is_normalized());
    }

    #[test]
    fn test_dot_and_cross() {
        let a = Vector2::new(1.0, 0.0);
        let b = Vector2::new(0.0, 1.0);

        assert_eq!(a.dot(b), 0.0);
        assert_eq!(a.cross(b), 1.0);
        assert_eq!(b.cross(a), -1.0);
    }

    #[test]
    fn test_angles() {
        let right = Vector2::RIGHT;
        let up = Vector2::UP;

        assert!((right.angle() - 0.0).abs() < f32::EPSILON);
        assert!((up.angle() - (-PI / 2.0)).abs() < f32::EPSILON);
        assert!((right.angle_to(up) - (-PI / 2.0)).abs() < f32::EPSILON);
    }

    #[test]
    fn test_interpolation() {
        let a = Vector2::new(0.0, 0.0);
        let b = Vector2::new(10.0, 10.0);

        let lerped = a.lerp(b, 0.5);
        assert_eq!(lerped, Vector2::new(5.0, 5.0));

        let moved = a.move_toward(b, 5.0);
        // move_toward moves 5 units toward b, which is at distance sqrt(200) ≈ 14.14
        // So we should move 5/14.14 of the way there
        let expected_x = 5.0 / (10.0 * std::f32::consts::SQRT_2) * 10.0;
        let expected_y = 5.0 / (10.0 * std::f32::consts::SQRT_2) * 10.0;
        assert!((moved.x - expected_x).abs() < 1e-5);
        assert!((moved.y - expected_y).abs() < 1e-5);
    }

    #[test]
    fn test_transformations() {
        let v = Vector2::new(1.0, 0.0);
        let rotated = v.rotated(PI / 2.0);

        assert!((rotated.x - 0.0).abs() < f32::EPSILON);
        assert!((rotated.y - 1.0).abs() < f32::EPSILON);

        let ortho = v.orthogonal();
        assert_eq!(ortho, Vector2::new(0.0, 1.0));
    }

    #[test]
    fn test_utility_functions() {
        let v = Vector2::new(-3.5, 4.7);

        assert_eq!(v.abs(), Vector2::new(3.5, 4.7));
        assert_eq!(v.floor(), Vector2::new(-4.0, 4.0));
        assert_eq!(v.ceil(), Vector2::new(-3.0, 5.0));
        assert_eq!(v.round(), Vector2::new(-4.0, 5.0));
        assert_eq!(v.sign(), Vector2::new(-1.0, 1.0));
    }

    #[test]
    fn test_clamping() {
        let v = Vector2::new(-1.0, 5.0);
        let min = Vector2::new(0.0, 0.0);
        let max = Vector2::new(3.0, 3.0);

        let clamped = v.clamp(min, max);
        assert_eq!(clamped, Vector2::new(0.0, 3.0));
    }

    #[test]
    fn test_comparison_functions() {
        let a = Vector2::new(1.0, 2.0);
        let b = Vector2::new(1.00001, 2.00001);

        assert!(a.is_equal_approx(b));
        assert!(a.is_finite());
        assert!(!Vector2::new(f32::INFINITY, 0.0).is_finite());

        let zero_approx = Vector2::new(0.000001, -0.000001);
        assert!(zero_approx.is_zero_approx());
    }

    #[test]
    fn test_indexing() {
        let mut v = Vector2::new(1.0, 2.0);

        assert_eq!(v[0], 1.0);
        assert_eq!(v[1], 2.0);
        assert_eq!(v[Axis::X], 1.0);
        assert_eq!(v[Axis::Y], 2.0);

        v[0] = 3.0;
        v[Axis::Y] = 4.0;
        assert_eq!(v, Vector2::new(3.0, 4.0));
    }

    #[test]
    fn test_conversions() {
        let v = Vector2::new(1.0, 2.0);

        let tuple: (f32, f32) = v.into();
        assert_eq!(tuple, (1.0, 2.0));

        let array: [f32; 2] = v.into();
        assert_eq!(array, [1.0, 2.0]);

        let from_tuple = Vector2::from((3.0, 4.0));
        assert_eq!(from_tuple, Vector2::new(3.0, 4.0));

        let from_array = Vector2::from([5.0, 6.0]);
        assert_eq!(from_array, Vector2::new(5.0, 6.0));
    }
}