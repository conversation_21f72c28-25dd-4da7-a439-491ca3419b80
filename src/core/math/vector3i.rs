//! Comprehensive 3D integer vector implementation for voxel systems and 3D grids.
//!
//! This module provides a complete 3D integer vector implementation optimized for
//! discrete 3D mathematics, voxel-based games, 3D tile systems, and spatial indexing
//! commonly used in 3D game development and scientific computing.

use std::fmt;
use super::Vector3;

/// A 3D vector using integer coordinates.
///
/// This struct represents a point or direction in 3D space with integer x, y, and z components.
/// It is optimized for voxel-based operations, 3D grid coordinates, spatial indexing, and
/// discrete 3D mathematical calculations where floating-point precision is not needed.
///
/// ## Use Cases
///
/// Vector3i is ideal for:
/// - **Voxel-based games**: Chunk coordinates, block positions, voxel indices
/// - **3D Grid systems**: Spatial partitioning, 3D cellular automata, pathfinding
/// - **Discrete 3D mathematics**: Integer-only calculations, counting, indexing
/// - **Memory-efficient storage**: Compact representation of 3D coordinates
/// - **Performance-critical code**: Avoiding floating-point operations in 3D space
///
/// ## Limitations
///
/// Unlike Vector3, Vector3i does not support:
/// - Normalization (integer vectors cannot have unit length)
/// - Rotation (requires trigonometric functions)
/// - Interpolation (requires floating-point arithmetic)
/// - Length calculation (would require square root)
///
/// For these operations, convert to Vector3 using `to_vector3()`.
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct Vector3i {
    /// The vector's X component (horizontal coordinate).
    pub x: i32,
    /// The vector's Y component (vertical coordinate).
    pub y: i32,
    /// The vector's Z component (depth coordinate).
    pub z: i32,
}

impl Vector3i {
    /// ### Zero vector constant -> Vector3i(0, 0, 0).
    ///
    /// Represents the additive identity for vector operations and the origin point in 3D space.
    /// This vector has zero magnitude and no defined direction.
    pub const ZERO: Vector3i = Vector3i { x: 0, y: 0, z: 0 };

    /// ### Unit vector constant -> Vector3i(1, 1, 1).
    ///
    /// A vector with all components set to 1, commonly used for uniform scaling
    /// and as a reference vector in 3D grid-based calculations.
    pub const ONE: Vector3i = Vector3i { x: 1, y: 1, z: 1 };

    /// ### Left-pointing unit vector constant -> Vector3i(-1, 0, 0).
    ///
    /// Points in the negative X direction with unit magnitude. Represents the standard
    /// leftward direction in 3D grid-based coordinate systems.
    pub const LEFT: Vector3i = Vector3i { x: -1, y: 0, z: 0 };

    /// ### Right-pointing unit vector constant -> Vector3i(1, 0, 0).
    ///
    /// Points in the positive X direction with unit magnitude. Represents the standard
    /// rightward direction and is the basis vector for the X-axis.
    pub const RIGHT: Vector3i = Vector3i { x: 1, y: 0, z: 0 };

    /// ### Upward-pointing unit vector constant -> Vector3i(0, 1, 0).
    ///
    /// Points in the positive Y direction with unit magnitude. In most 3D coordinate systems,
    /// this represents the upward direction and is the basis vector for the Y-axis.
    pub const UP: Vector3i = Vector3i { x: 0, y: 1, z: 0 };

    /// ### Downward-pointing unit vector constant -> Vector3i(0, -1, 0).
    ///
    /// Points in the negative Y direction with unit magnitude. Represents the downward
    /// direction in most 3D coordinate systems.
    pub const DOWN: Vector3i = Vector3i { x: 0, y: -1, z: 0 };

    /// ### Forward-pointing unit vector constant -> Vector3i(0, 0, -1).
    ///
    /// Points in the negative Z direction with unit magnitude. In right-handed coordinate systems,
    /// this typically represents the forward direction (into the screen).
    pub const FORWARD: Vector3i = Vector3i { x: 0, y: 0, z: -1 };

    /// ### Backward-pointing unit vector constant -> Vector3i(0, 0, 1).
    ///
    /// Points in the positive Z direction with unit magnitude. In right-handed coordinate systems,
    /// this typically represents the backward direction (out of the screen).
    pub const BACK: Vector3i = Vector3i { x: 0, y: 0, z: 1 };

    /// ### Creates a new Vector3i with the specified x, y, and z components.
    ///
    /// This is the primary constructor for creating 3D integer vectors with explicit coordinate values.
    /// All components can be any 32-bit signed integer value, including negative numbers and zero.
    ///
    /// # Arguments
    /// * `x` - The horizontal component (X-coordinate)
    /// * `y` - The vertical component (Y-coordinate)
    /// * `z` - The depth component (Z-coordinate)
    ///
    /// # Returns
    /// A new Vector3i instance with the specified components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let voxel_pos = Vector3i::new(5, 3, 7);
    /// assert_eq!(voxel_pos.x, 5);
    /// assert_eq!(voxel_pos.y, 3);
    /// assert_eq!(voxel_pos.z, 7);
    ///
    /// // Negative coordinates for relative positions
    /// let offset = Vector3i::new(-2, 1, -3);
    /// assert_eq!(offset.x, -2);
    /// ```
    #[inline]
    pub const fn new(x: i32, y: i32, z: i32) -> Self {
        Vector3i { x, y, z }
    }

    /// ### Calculates the squared length of the vector.
    ///
    /// This is the only length-related operation available for integer vectors,
    /// as the actual length would require floating-point square root calculation.
    /// Useful for distance comparisons and magnitude-based operations.
    ///
    /// # Returns
    /// The squared length of the vector (x² + y² + z²).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let v = Vector3i::new(3, 4, 0);
    /// assert_eq!(v.length_squared(), 25); // 3² + 4² + 0² = 9 + 16 + 0 = 25
    ///
    /// // Useful for distance comparisons without square root
    /// let pos1 = Vector3i::new(0, 0, 0);
    /// let pos2 = Vector3i::new(3, 4, 0);
    /// let distance_sq = (pos2 - pos1).length_squared();
    /// assert_eq!(distance_sq, 25);
    /// ```
    #[inline]
    pub fn length_squared(self) -> i32 {
        self.x * self.x + self.y * self.y + self.z * self.z
    }

    /// ### Calculates the dot product with another vector.
    ///
    /// The dot product is useful for determining the relationship between two vectors,
    /// such as whether they point in similar directions or are perpendicular.
    ///
    /// # Arguments
    /// * `other` - The other vector to compute the dot product with
    ///
    /// # Returns
    /// The dot product as a 32-bit signed integer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let v1 = Vector3i::new(2, 3, 1);
    /// let v2 = Vector3i::new(4, 1, 2);
    /// assert_eq!(v1.dot(v2), 13); // 2*4 + 3*1 + 1*2 = 8 + 3 + 2 = 13
    ///
    /// // Perpendicular vectors have zero dot product
    /// let right = Vector3i::RIGHT;
    /// let up = Vector3i::UP;
    /// assert_eq!(right.dot(up), 0);
    /// ```
    #[inline]
    pub fn dot(self, other: Vector3i) -> i32 {
        self.x * other.x + self.y * other.y + self.z * other.z
    }

    /// ### Calculates the cross product with another vector.
    ///
    /// The cross product returns a vector perpendicular to both input vectors.
    /// In 3D, this produces a proper 3D cross product vector.
    ///
    /// # Arguments
    /// * `other` - The other vector to compute the cross product with
    ///
    /// # Returns
    /// A new Vector3i perpendicular to both input vectors.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let v1 = Vector3i::new(1, 0, 0);
    /// let v2 = Vector3i::new(0, 1, 0);
    /// let cross = v1.cross(v2);
    /// assert_eq!(cross, Vector3i::new(0, 0, 1));
    ///
    /// // Cross product with basis vectors
    /// let right = Vector3i::RIGHT;
    /// let up = Vector3i::UP;
    /// let forward = right.cross(up);
    /// assert_eq!(forward, Vector3i::FORWARD);
    /// ```
    #[inline]
    pub fn cross(self, other: Vector3i) -> Vector3i {
        Vector3i::new(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x,
        )
    }

    /// ### Converts this integer vector to a floating-point Vector3.
    ///
    /// This conversion allows access to floating-point operations like normalization,
    /// rotation, and interpolation that are not available for integer vectors.
    ///
    /// # Returns
    /// A new Vector3 with the same component values as floating-point numbers.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector3i, Vector3};
    /// let int_vec = Vector3i::new(3, 4, 5);
    /// let float_vec = int_vec.to_vector3();
    /// assert_eq!(float_vec, Vector3::new(3.0, 4.0, 5.0));
    ///
    /// // Now can use floating-point operations
    /// let length = float_vec.length();
    /// let normalized = float_vec.normalized();
    /// ```
    #[inline]
    pub fn to_vector3(self) -> Vector3 {
        Vector3::new(self.x as f32, self.y as f32, self.z as f32)
    }

    /// ### Returns a vector with the absolute values of the components.
    ///
    /// Creates a new vector where all x, y, and z components are their absolute values.
    /// This is useful for distance calculations and ensuring positive coordinates
    /// in 3D grid-based systems.
    ///
    /// # Returns
    /// A new Vector3i with absolute values of all components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let v = Vector3i::new(-3, -4, -5);
    /// let abs_v = v.abs();
    /// assert_eq!(abs_v, Vector3i::new(3, 4, 5));
    ///
    /// // Useful for 3D grid distance calculations
    /// let pos1 = Vector3i::new(2, 5, 1);
    /// let pos2 = Vector3i::new(7, 1, 4);
    /// let distance_vec = (pos2 - pos1).abs();
    /// assert_eq!(distance_vec, Vector3i::new(5, 4, 3)); // Manhattan distance components
    /// ```
    #[inline]
    pub fn abs(self) -> Vector3i {
        Vector3i::new(self.x.abs(), self.y.abs(), self.z.abs())
    }

    /// ### Returns a vector with the signs of the components.
    ///
    /// Each component becomes -1 if negative, 0 if zero, or 1 if positive.
    /// This is useful for determining direction and creating unit direction vectors.
    ///
    /// # Returns
    /// A new Vector3i with sign values (-1, 0, or 1) for each component.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let v = Vector3i::new(-5, 3, 0);
    /// let sign_v = v.sign();
    /// assert_eq!(sign_v, Vector3i::new(-1, 1, 0));
    ///
    /// let zero_v = Vector3i::new(0, -7, 2);
    /// let sign_zero = zero_v.sign();
    /// assert_eq!(sign_zero, Vector3i::new(0, -1, 1));
    /// ```
    #[inline]
    pub fn sign(self) -> Vector3i {
        Vector3i::new(self.x.signum(), self.y.signum(), self.z.signum())
    }

    /// ### Returns the component-wise minimum of this vector and another vector.
    ///
    /// Creates a new vector where each component is the minimum of the corresponding
    /// components from both vectors. Useful for 3D bounding box calculations.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// A new Vector3i with the minimum components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let v1 = Vector3i::new(1, 5, 3);
    /// let v2 = Vector3i::new(3, 2, 7);
    /// let min_v = v1.min(v2);
    /// assert_eq!(min_v, Vector3i::new(1, 2, 3));
    ///
    /// // Useful for 3D bounding box calculations
    /// let corner1 = Vector3i::new(10, 10, 10);
    /// let corner2 = Vector3i::new(5, 15, 8);
    /// let min_corner = corner1.min(corner2);
    /// assert_eq!(min_corner, Vector3i::new(5, 10, 8));
    /// ```
    #[inline]
    pub fn min(self, other: Vector3i) -> Vector3i {
        Vector3i::new(
            self.x.min(other.x),
            self.y.min(other.y),
            self.z.min(other.z),
        )
    }

    /// ### Returns the component-wise maximum of this vector and another vector.
    ///
    /// Creates a new vector where each component is the maximum of the corresponding
    /// components from both vectors. Useful for 3D bounding box calculations.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// A new Vector3i with the maximum components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let v1 = Vector3i::new(1, 5, 3);
    /// let v2 = Vector3i::new(3, 2, 7);
    /// let max_v = v1.max(v2);
    /// assert_eq!(max_v, Vector3i::new(3, 5, 7));
    ///
    /// // Useful for 3D bounding box calculations
    /// let corner1 = Vector3i::new(10, 10, 10);
    /// let corner2 = Vector3i::new(5, 15, 8);
    /// let max_corner = corner1.max(corner2);
    /// assert_eq!(max_corner, Vector3i::new(10, 15, 10));
    /// ```
    #[inline]
    pub fn max(self, other: Vector3i) -> Vector3i {
        Vector3i::new(
            self.x.max(other.x),
            self.y.max(other.y),
            self.z.max(other.z),
        )
    }

    /// ### Clamps the vector components between minimum and maximum vectors.
    ///
    /// Each component is clamped independently between the corresponding components
    /// of the min and max vectors. Useful for constraining positions within 3D bounds.
    ///
    /// # Arguments
    /// * `min` - The minimum vector (component-wise lower bounds)
    /// * `max` - The maximum vector (component-wise upper bounds)
    ///
    /// # Returns
    /// A new Vector3i with components clamped between min and max.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let pos = Vector3i::new(-5, 15, 25);
    /// let min_bounds = Vector3i::new(0, 0, 0);
    /// let max_bounds = Vector3i::new(10, 10, 20);
    /// let clamped = pos.clamp(min_bounds, max_bounds);
    /// assert_eq!(clamped, Vector3i::new(0, 10, 20));
    ///
    /// // Useful for keeping entities within 3D world bounds
    /// let player_pos = Vector3i::new(25, -3, 15);
    /// let world_min = Vector3i::new(0, 0, 0);
    /// let world_max = Vector3i::new(20, 20, 20);
    /// let valid_pos = player_pos.clamp(world_min, world_max);
    /// assert_eq!(valid_pos, Vector3i::new(20, 0, 15));
    /// ```
    #[inline]
    pub fn clamp(self, min: Vector3i, max: Vector3i) -> Vector3i {
        Vector3i::new(
            self.x.clamp(min.x, max.x),
            self.y.clamp(min.y, max.y),
            self.z.clamp(min.z, max.z),
        )
    }

    /// ### Calculates the Manhattan distance to another vector.
    ///
    /// The Manhattan distance (also known as taxicab distance) is the sum of the
    /// absolute differences of the coordinates. This is often more useful than
    /// Euclidean distance for 3D grid-based games and pathfinding.
    ///
    /// # Arguments
    /// * `other` - The other vector to calculate distance to
    ///
    /// # Returns
    /// The Manhattan distance as a positive integer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let pos1 = Vector3i::new(2, 3, 1);
    /// let pos2 = Vector3i::new(5, 7, 4);
    /// let distance = pos1.manhattan_distance(pos2);
    /// assert_eq!(distance, 10); // |5-2| + |7-3| + |4-1| = 3 + 4 + 3 = 10
    ///
    /// // Useful for 3D grid-based pathfinding
    /// let start = Vector3i::new(0, 0, 0);
    /// let goal = Vector3i::new(3, 4, 2);
    /// let min_moves = start.manhattan_distance(goal);
    /// assert_eq!(min_moves, 9); // Minimum moves in 3D grid
    /// ```
    #[inline]
    pub fn manhattan_distance(self, other: Vector3i) -> i32 {
        (self.x - other.x).abs() + (self.y - other.y).abs() + (self.z - other.z).abs()
    }

    /// ### Calculates the Chebyshev distance to another vector.
    ///
    /// The Chebyshev distance (also known as chessboard distance) is the maximum
    /// of the absolute differences of the coordinates. This represents the minimum
    /// number of moves needed to reach one point from another if diagonal moves are allowed.
    ///
    /// # Arguments
    /// * `other` - The other vector to calculate distance to
    ///
    /// # Returns
    /// The Chebyshev distance as a positive integer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let pos1 = Vector3i::new(2, 3, 1);
    /// let pos2 = Vector3i::new(5, 7, 4);
    /// let distance = pos1.chebyshev_distance(pos2);
    /// assert_eq!(distance, 4); // max(|5-2|, |7-3|, |4-1|) = max(3, 4, 3) = 4
    ///
    /// // Useful for 3D movement with diagonal moves allowed
    /// let start = Vector3i::new(0, 0, 0);
    /// let goal = Vector3i::new(3, 4, 2);
    /// let moves_needed = start.chebyshev_distance(goal);
    /// assert_eq!(moves_needed, 4); // Can move diagonally in 3D
    /// ```
    #[inline]
    pub fn chebyshev_distance(self, other: Vector3i) -> i32 {
        (self.x - other.x)
            .abs()
            .max((self.y - other.y).abs())
            .max((self.z - other.z).abs())
    }

    /// ### Checks if this vector is exactly equal to another vector.
    ///
    /// For integer vectors, exact equality is meaningful unlike floating-point vectors.
    /// This method is provided for API consistency with Vector3, though direct
    /// comparison with `==` is equivalent and more idiomatic for integer types.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// `true` if the vectors are exactly equal, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let v1 = Vector3i::new(5, 3, 7);
    /// let v2 = Vector3i::new(5, 3, 7);
    /// let v3 = Vector3i::new(5, 4, 7);
    ///
    /// assert!(v1.is_equal_approx(v2));
    /// assert!(!v1.is_equal_approx(v3));
    ///
    /// // Equivalent to direct comparison for integers
    /// assert_eq!(v1 == v2, v1.is_equal_approx(v2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Vector3i) -> bool {
        self.x == other.x && self.y == other.y && self.z == other.z
    }

    /// ### Checks if the vector is exactly zero.
    ///
    /// For integer vectors, zero checking is exact unlike floating-point vectors.
    /// This method is provided for API consistency with Vector3.
    ///
    /// # Returns
    /// `true` if all components are exactly zero, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector3i;
    /// let zero = Vector3i::ZERO;
    /// assert!(zero.is_zero_approx());
    ///
    /// let not_zero = Vector3i::new(1, 0, 0);
    /// assert!(!not_zero.is_zero_approx());
    ///
    /// // Equivalent to direct comparison with ZERO
    /// assert_eq!(zero == Vector3i::ZERO, zero.is_zero_approx());
    /// ```
    #[inline]
    pub fn is_zero_approx(self) -> bool {
        self.x == 0 && self.y == 0 && self.z == 0
    }
}

// ============================================================================
// ARITHMETIC OPERATIONS
// ============================================================================

impl std::ops::Add for Vector3i {
    type Output = Vector3i;

    #[inline]
    fn add(self, other: Vector3i) -> Vector3i {
        Vector3i::new(self.x + other.x, self.y + other.y, self.z + other.z)
    }
}

impl std::ops::Sub for Vector3i {
    type Output = Vector3i;

    #[inline]
    fn sub(self, other: Vector3i) -> Vector3i {
        Vector3i::new(self.x - other.x, self.y - other.y, self.z - other.z)
    }
}

impl std::ops::Mul<i32> for Vector3i {
    type Output = Vector3i;

    #[inline]
    fn mul(self, scalar: i32) -> Vector3i {
        Vector3i::new(self.x * scalar, self.y * scalar, self.z * scalar)
    }
}

impl std::ops::Mul<Vector3i> for i32 {
    type Output = Vector3i;

    #[inline]
    fn mul(self, vector: Vector3i) -> Vector3i {
        Vector3i::new(self * vector.x, self * vector.y, self * vector.z)
    }
}

impl std::ops::Div<i32> for Vector3i {
    type Output = Vector3i;

    #[inline]
    fn div(self, scalar: i32) -> Vector3i {
        Vector3i::new(self.x / scalar, self.y / scalar, self.z / scalar)
    }
}

impl std::ops::Neg for Vector3i {
    type Output = Vector3i;

    #[inline]
    fn neg(self) -> Vector3i {
        Vector3i::new(-self.x, -self.y, -self.z)
    }
}

impl fmt::Display for Vector3i {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "({}, {}, {})", self.x, self.y, self.z)
    }
}
