//! Comprehensive 4D integer vector implementation for discrete 4D mathematics and color representation.
//!
//! This module provides a complete 4D integer vector implementation optimized for
//! discrete 4D mathematics, integer color representation (RGBA), 4D grid systems,
//! and performance-critical applications where floating-point precision is not needed.

use std::fmt;
use super::Vector4;

/// A 4D vector using integer coordinates.
///
/// This struct represents a point or direction in 4D space with integer x, y, z, and w components.
/// It is optimized for discrete 4D operations, integer color representation, 4D grid coordinates,
/// and mathematical calculations where exact integer arithmetic is required.
///
/// ## Use Cases
///
/// Vector4i is ideal for:
/// - **Integer Color Representation**: RGBA values with integer components (0-255)
/// - **4D Grid Systems**: Discrete 4D spatial indexing, hypercube coordinates
/// - **Discrete Mathematics**: Integer-only calculations, counting, indexing
/// - **Memory-efficient storage**: Compact representation of 4D integer coordinates
/// - **Performance-critical code**: Avoiding floating-point operations in 4D space
///
/// ## Component Interpretations
///
/// Depending on the use case, components can represent:
/// - **Color**: (x, y, z, w) = (r, g, b, a) with values typically 0-255
/// - **4D Grid**: (x, y, z, w) = discrete coordinates in 4D space
/// - **Indices**: (x, y, z, w) = multi-dimensional array indices
/// - **Counters**: (x, y, z, w) = separate integer counters or accumulators
///
/// ## Limitations
///
/// Unlike Vector4, Vector4i does not support:
/// - Normalization (integer vectors cannot have unit length)
/// - Rotation (requires trigonometric functions)
/// - Interpolation (requires floating-point arithmetic)
/// - Length calculation (would require square root)
///
/// For these operations, convert to Vector4 using `to_vector4()`.
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct Vector4i {
    /// The vector's X component (first coordinate).
    pub x: i32,
    /// The vector's Y component (second coordinate).
    pub y: i32,
    /// The vector's Z component (third coordinate).
    pub z: i32,
    /// The vector's W component (fourth coordinate).
    pub w: i32,
}

impl Vector4i {
    /// ### Zero vector constant -> Vector4i(0, 0, 0, 0).
    ///
    /// Represents the additive identity for vector operations and the origin point in 4D space.
    /// This vector has zero magnitude and no defined direction.
    pub const ZERO: Vector4i = Vector4i { x: 0, y: 0, z: 0, w: 0 };

    /// ### Unit vector constant -> Vector4i(1, 1, 1, 1).
    ///
    /// A vector with all components set to 1, commonly used for uniform scaling
    /// and as a reference vector in 4D grid-based calculations.
    pub const ONE: Vector4i = Vector4i { x: 1, y: 1, z: 1, w: 1 };

    /// ### Creates a new Vector4i with the specified x, y, z, and w components.
    ///
    /// This is the primary constructor for creating 4D integer vectors with explicit coordinate values.
    /// All components can be any 32-bit signed integer value, including negative numbers and zero.
    ///
    /// # Arguments
    /// * `x` - The first component (X-coordinate)
    /// * `y` - The second component (Y-coordinate)
    /// * `z` - The third component (Z-coordinate)
    /// * `w` - The fourth component (W-coordinate)
    ///
    /// # Returns
    /// A new Vector4i instance with the specified components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4i;
    /// let color = Vector4i::new(255, 128, 64, 255);
    /// assert_eq!(color.x, 255); // Red component
    /// assert_eq!(color.w, 255); // Alpha component
    ///
    /// // 4D grid coordinates
    /// let grid_pos = Vector4i::new(5, 3, 7, 2);
    /// assert_eq!(grid_pos.x, 5);
    /// ```
    #[inline]
    pub const fn new(x: i32, y: i32, z: i32, w: i32) -> Self {
        Vector4i { x, y, z, w }
    }

    /// ### Calculates the squared length of the vector.
    ///
    /// This is the only length-related operation available for integer vectors,
    /// as the actual length would require floating-point square root calculation.
    /// Useful for distance comparisons and magnitude-based operations.
    ///
    /// # Returns
    /// The squared length of the vector (x² + y² + z² + w²).
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4i;
    /// let v = Vector4i::new(2, 0, 0, 0);
    /// assert_eq!(v.length_squared(), 4); // 2² + 0² + 0² + 0² = 4
    ///
    /// // Useful for distance comparisons without square root
    /// let pos1 = Vector4i::new(0, 0, 0, 0);
    /// let pos2 = Vector4i::new(2, 0, 0, 0);
    /// let distance_sq = (pos2 - pos1).length_squared();
    /// assert_eq!(distance_sq, 4);
    /// ```
    #[inline]
    pub fn length_squared(self) -> i32 {
        self.x * self.x + self.y * self.y + self.z * self.z + self.w * self.w
    }

    /// ### Calculates the dot product with another vector.
    ///
    /// The dot product is useful for determining the relationship between two vectors,
    /// such as whether they point in similar directions or are perpendicular.
    ///
    /// # Arguments
    /// * `other` - The other vector to compute the dot product with
    ///
    /// # Returns
    /// The dot product as a 32-bit signed integer.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4i;
    /// let v1 = Vector4i::new(1, 2, 3, 4);
    /// let v2 = Vector4i::new(5, 6, 7, 8);
    /// assert_eq!(v1.dot(v2), 70); // 1*5 + 2*6 + 3*7 + 4*8 = 5 + 12 + 21 + 32 = 70
    ///
    /// // Zero dot product indicates perpendicularity
    /// let v3 = Vector4i::new(1, 0, 0, 0);
    /// let v4 = Vector4i::new(0, 1, 0, 0);
    /// assert_eq!(v3.dot(v4), 0);
    /// ```
    #[inline]
    pub fn dot(self, other: Vector4i) -> i32 {
        self.x * other.x + self.y * other.y + self.z * other.z + self.w * other.w
    }

    /// ### Converts this integer vector to a floating-point Vector4.
    ///
    /// This conversion allows access to floating-point operations like normalization,
    /// rotation, and interpolation that are not available for integer vectors.
    ///
    /// # Returns
    /// A new Vector4 with the same component values as floating-point numbers.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::{Vector4i, Vector4};
    /// let int_vec = Vector4i::new(255, 128, 64, 255);
    /// let float_vec = int_vec.to_vector4();
    /// assert_eq!(float_vec, Vector4::new(255.0, 128.0, 64.0, 255.0));
    ///
    /// // Now can use floating-point operations
    /// let length = float_vec.length();
    /// let normalized = float_vec.normalized();
    /// ```
    #[inline]
    pub fn to_vector4(self) -> Vector4 {
        Vector4::new(self.x as f32, self.y as f32, self.z as f32, self.w as f32)
    }

    /// ### Returns a vector with the absolute values of the components.
    ///
    /// Creates a new vector where all x, y, z, and w components are their absolute values.
    /// This is useful for distance calculations and ensuring positive coordinates
    /// in 4D grid-based systems.
    ///
    /// # Returns
    /// A new Vector4i with absolute values of all components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4i;
    /// let v = Vector4i::new(-1, -2, -3, -4);
    /// let abs_v = v.abs();
    /// assert_eq!(abs_v, Vector4i::new(1, 2, 3, 4));
    ///
    /// // Useful for 4D grid distance calculations
    /// let pos1 = Vector4i::new(2, 5, 1, 3);
    /// let pos2 = Vector4i::new(7, 1, 4, 8);
    /// let distance_vec = (pos2 - pos1).abs();
    /// assert_eq!(distance_vec, Vector4i::new(5, 4, 3, 5)); // Distance components
    /// ```
    #[inline]
    pub fn abs(self) -> Vector4i {
        Vector4i::new(self.x.abs(), self.y.abs(), self.z.abs(), self.w.abs())
    }

    /// ### Returns a vector with the signs of the components.
    ///
    /// Each component becomes -1 if negative, 0 if zero, or 1 if positive.
    /// This is useful for determining direction and creating unit direction vectors.
    ///
    /// # Returns
    /// A new Vector4i with sign values (-1, 0, or 1) for each component.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4i;
    /// let v = Vector4i::new(-5, 3, 0, -2);
    /// let sign_v = v.sign();
    /// assert_eq!(sign_v, Vector4i::new(-1, 1, 0, -1));
    ///
    /// let zero_v = Vector4i::new(0, -7, 2, 0);
    /// let sign_zero = zero_v.sign();
    /// assert_eq!(sign_zero, Vector4i::new(0, -1, 1, 0));
    /// ```
    #[inline]
    pub fn sign(self) -> Vector4i {
        Vector4i::new(
            self.x.signum(),
            self.y.signum(),
            self.z.signum(),
            self.w.signum(),
        )
    }

    /// ### Returns the component-wise minimum of this vector and another vector.
    ///
    /// Creates a new vector where each component is the minimum of the corresponding
    /// components from both vectors. Useful for 4D bounding box calculations.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// A new Vector4i with the minimum components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4i;
    /// let v1 = Vector4i::new(1, 5, 3, 8);
    /// let v2 = Vector4i::new(3, 2, 7, 4);
    /// let min_v = v1.min(v2);
    /// assert_eq!(min_v, Vector4i::new(1, 2, 3, 4));
    ///
    /// // Useful for color component clamping
    /// let color1 = Vector4i::new(255, 100, 50, 255);
    /// let color2 = Vector4i::new(128, 200, 75, 128);
    /// let min_color = color1.min(color2);
    /// assert_eq!(min_color, Vector4i::new(128, 100, 50, 128));
    /// ```
    #[inline]
    pub fn min(self, other: Vector4i) -> Vector4i {
        Vector4i::new(
            self.x.min(other.x),
            self.y.min(other.y),
            self.z.min(other.z),
            self.w.min(other.w),
        )
    }

    /// ### Returns the component-wise maximum of this vector and another vector.
    ///
    /// Creates a new vector where each component is the maximum of the corresponding
    /// components from both vectors. Useful for 4D bounding box calculations.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// A new Vector4i with the maximum components.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4i;
    /// let v1 = Vector4i::new(1, 5, 3, 8);
    /// let v2 = Vector4i::new(3, 2, 7, 4);
    /// let max_v = v1.max(v2);
    /// assert_eq!(max_v, Vector4i::new(3, 5, 7, 8));
    ///
    /// // Useful for color component operations
    /// let color1 = Vector4i::new(255, 100, 50, 255);
    /// let color2 = Vector4i::new(128, 200, 75, 128);
    /// let max_color = color1.max(color2);
    /// assert_eq!(max_color, Vector4i::new(255, 200, 75, 255));
    /// ```
    #[inline]
    pub fn max(self, other: Vector4i) -> Vector4i {
        Vector4i::new(
            self.x.max(other.x),
            self.y.max(other.y),
            self.z.max(other.z),
            self.w.max(other.w),
        )
    }

    /// ### Clamps the vector components between minimum and maximum vectors.
    ///
    /// Each component is clamped independently between the corresponding components
    /// of the min and max vectors. Useful for constraining values within 4D bounds.
    ///
    /// # Arguments
    /// * `min` - The minimum vector (component-wise lower bounds)
    /// * `max` - The maximum vector (component-wise upper bounds)
    ///
    /// # Returns
    /// A new Vector4i with components clamped between min and max.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4i;
    /// let color = Vector4i::new(-50, 300, 128, 400);
    /// let min_bounds = Vector4i::new(0, 0, 0, 0);
    /// let max_bounds = Vector4i::new(255, 255, 255, 255);
    /// let clamped = color.clamp(min_bounds, max_bounds);
    /// assert_eq!(clamped, Vector4i::new(0, 255, 128, 255));
    ///
    /// // Useful for keeping color values within valid range
    /// let invalid_color = Vector4i::new(300, -10, 128, 500);
    /// let valid_color = invalid_color.clamp(Vector4i::ZERO, Vector4i::new(255, 255, 255, 255));
    /// assert_eq!(valid_color, Vector4i::new(255, 0, 128, 255));
    /// ```
    #[inline]
    pub fn clamp(self, min: Vector4i, max: Vector4i) -> Vector4i {
        Vector4i::new(
            self.x.clamp(min.x, max.x),
            self.y.clamp(min.y, max.y),
            self.z.clamp(min.z, max.z),
            self.w.clamp(min.w, max.w),
        )
    }

    /// ### Checks if this vector is exactly equal to another vector.
    ///
    /// For integer vectors, exact equality is meaningful unlike floating-point vectors.
    /// This method is provided for API consistency with Vector4, though direct
    /// comparison with `==` is equivalent and more idiomatic for integer types.
    ///
    /// # Arguments
    /// * `other` - The other vector to compare with
    ///
    /// # Returns
    /// `true` if the vectors are exactly equal, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4i;
    /// let v1 = Vector4i::new(255, 128, 64, 255);
    /// let v2 = Vector4i::new(255, 128, 64, 255);
    /// let v3 = Vector4i::new(255, 128, 65, 255);
    ///
    /// assert!(v1.is_equal_approx(v2));
    /// assert!(!v1.is_equal_approx(v3));
    ///
    /// // Equivalent to direct comparison for integers
    /// assert_eq!(v1 == v2, v1.is_equal_approx(v2));
    /// ```
    #[inline]
    pub fn is_equal_approx(self, other: Vector4i) -> bool {
        self.x == other.x && self.y == other.y && self.z == other.z && self.w == other.w
    }

    /// ### Checks if the vector is exactly zero.
    ///
    /// For integer vectors, zero checking is exact unlike floating-point vectors.
    /// This method is provided for API consistency with Vector4.
    ///
    /// # Returns
    /// `true` if all components are exactly zero, `false` otherwise.
    ///
    /// # Examples
    /// ```
    /// # use verturion::core::math::Vector4i;
    /// let zero = Vector4i::ZERO;
    /// assert!(zero.is_zero_approx());
    ///
    /// let not_zero = Vector4i::new(1, 0, 0, 0);
    /// assert!(!not_zero.is_zero_approx());
    ///
    /// // Equivalent to direct comparison with ZERO
    /// assert_eq!(zero == Vector4i::ZERO, zero.is_zero_approx());
    /// ```
    #[inline]
    pub fn is_zero_approx(self) -> bool {
        self.x == 0 && self.y == 0 && self.z == 0 && self.w == 0
    }
}

// ============================================================================
// ARITHMETIC OPERATIONS
// ============================================================================

impl std::ops::Add for Vector4i {
    type Output = Vector4i;

    #[inline]
    fn add(self, other: Vector4i) -> Vector4i {
        Vector4i::new(
            self.x + other.x,
            self.y + other.y,
            self.z + other.z,
            self.w + other.w,
        )
    }
}

impl std::ops::Sub for Vector4i {
    type Output = Vector4i;

    #[inline]
    fn sub(self, other: Vector4i) -> Vector4i {
        Vector4i::new(
            self.x - other.x,
            self.y - other.y,
            self.z - other.z,
            self.w - other.w,
        )
    }
}

impl std::ops::Mul<i32> for Vector4i {
    type Output = Vector4i;

    #[inline]
    fn mul(self, scalar: i32) -> Vector4i {
        Vector4i::new(
            self.x * scalar,
            self.y * scalar,
            self.z * scalar,
            self.w * scalar,
        )
    }
}

impl std::ops::Mul<Vector4i> for i32 {
    type Output = Vector4i;

    #[inline]
    fn mul(self, vector: Vector4i) -> Vector4i {
        Vector4i::new(
            self * vector.x,
            self * vector.y,
            self * vector.z,
            self * vector.w,
        )
    }
}

impl std::ops::Div<i32> for Vector4i {
    type Output = Vector4i;

    #[inline]
    fn div(self, scalar: i32) -> Vector4i {
        Vector4i::new(
            self.x / scalar,
            self.y / scalar,
            self.z / scalar,
            self.w / scalar,
        )
    }
}

impl std::ops::Neg for Vector4i {
    type Output = Vector4i;

    #[inline]
    fn neg(self) -> Vector4i {
        Vector4i::new(-self.x, -self.y, -self.z, -self.w)
    }
}

impl fmt::Display for Vector4i {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "({}, {}, {}, {})", self.x, self.y, self.z, self.w)
    }
}
