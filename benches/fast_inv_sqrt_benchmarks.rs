use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use verturion::core::math::Vector2;

// Import the fast inverse square root implementations
// Note: These are private functions, so we'll need to expose them for benchmarking
// For now, we'll implement them directly in the benchmark file

/// Standard library implementation for comparison
#[inline]
fn std_inv_sqrt(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    1.0 / x.sqrt()
}

/// Classic Quake III fast inverse square root with optimized Newton-Raphson iterations
#[inline]
fn fast_inv_sqrt_v1_quake_optimized(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let x_half = 0.5 * x;
    let mut i = x.to_bits();
    i = 0x5f3759df - (i >> 1);
    let mut y = f32::from_bits(i);

    y = y * (1.5 - x_half * y * y);
    y = y * (1.5 - x_half * y * y);

    y
}

/// Improved magic constant with single Newton-Raphson iteration
#[inline]
fn fast_inv_sqrt_v2_improved_magic(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let x_half = 0.5 * x;
    let mut i = x.to_bits();
    i = 0x5f375a86 - (i >> 1);
    let mut y = f32::from_bits(i);

    y = y * (1.5 - x_half * y * y);

    y
}

/// Polynomial approximation using Chebyshev coefficients
#[inline]
fn fast_inv_sqrt_v3_chebyshev(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let exp = ((x.to_bits() >> 23) & 0xff) as i32 - 127;
    let normalized = f32::from_bits((x.to_bits() & 0x807fffff) | 0x3f000000);
    
    let t = 2.0 * normalized - 1.5;
    
    let c0 = 1.4142135623730951;
    let c1 = -0.35355339059327373;
    let c2 = 0.08838834764831843;
    let c3 = -0.02209708691207961;
    let c4 = 0.005524271728019903;
    
    let poly = c0 + t * (c1 + t * (c2 + t * (c3 + t * c4)));
    
    let scale = if exp % 2 == 0 {
        2.0_f32.powi(-exp / 2)
    } else {
        2.0_f32.powi(-(exp + 1) / 2) * std::f32::consts::FRAC_1_SQRT_2
    };
    
    poly * scale
}

/// Hybrid approach combining bit manipulation with polynomial refinement
#[inline]
fn fast_inv_sqrt_v5_hybrid_polynomial(x: f32) -> f32 {
    if x == 0.0 {
        return 0.0;
    }
    if x < 0.0 {
        return f32::NAN;
    }
    if x < 1e-10 {
        return 1.0 / x.sqrt();
    }

    let mut i = x.to_bits();
    i = 0x5f375a86 - (i >> 1);
    let y0 = f32::from_bits(i);
    
    let t = x * y0 * y0 - 1.0;
    
    let a = 1.0;
    let b = -0.5;
    let c = 0.375;
    
    let correction = a + t * (b + t * c);
    y0 * correction
}

/// Generate test values across different ranges
fn generate_test_values() -> Vec<f32> {
    let mut values = Vec::new();
    
    // Small values
    for i in 1..=100 {
        values.push(i as f32 * 1e-6);
    }
    
    // Normal range values
    for i in 1..=1000 {
        values.push(i as f32 * 0.01);
    }
    
    // Large values
    for i in 1..=100 {
        values.push(i as f32 * 100.0);
    }
    
    // Very large values
    for i in 1..=50 {
        values.push(i as f32 * 10000.0);
    }
    
    values
}

/// Calculate accuracy metrics for a given implementation
fn calculate_accuracy_metrics<F>(func: F, test_values: &[f32]) -> (f32, f32, f32, f32)
where
    F: Fn(f32) -> f32,
{
    let mut max_error = 0.0;
    let mut sum_error = 0.0;
    let mut worst_input = 0.0;
    let mut count = 0;

    for &x in test_values {
        if x > 0.0 && x.is_finite() {
            let fast_result = func(x);
            let accurate_result = 1.0 / x.sqrt();
            
            if fast_result.is_finite() && accurate_result.is_finite() && accurate_result != 0.0 {
                let relative_error = ((fast_result - accurate_result) / accurate_result).abs();
                
                if relative_error > max_error {
                    max_error = relative_error;
                    worst_input = x;
                }
                
                sum_error += relative_error;
                count += 1;
            }
        }
    }

    let avg_error = if count > 0 { sum_error / count as f32 } else { 0.0 };
    (max_error, avg_error, worst_input, count as f32)
}

fn benchmark_fast_inv_sqrt_implementations(c: &mut Criterion) {
    let test_values = generate_test_values();
    
    // Performance benchmarks
    let mut group = c.benchmark_group("fast_inv_sqrt_performance");
    
    group.bench_function("std_library", |b| {
        b.iter(|| {
            for &x in &test_values {
                black_box(std_inv_sqrt(black_box(x)));
            }
        })
    });
    
    group.bench_function("v1_quake_optimized", |b| {
        b.iter(|| {
            for &x in &test_values {
                black_box(fast_inv_sqrt_v1_quake_optimized(black_box(x)));
            }
        })
    });
    
    group.bench_function("v2_improved_magic", |b| {
        b.iter(|| {
            for &x in &test_values {
                black_box(fast_inv_sqrt_v2_improved_magic(black_box(x)));
            }
        })
    });
    
    group.bench_function("v3_chebyshev", |b| {
        b.iter(|| {
            for &x in &test_values {
                black_box(fast_inv_sqrt_v3_chebyshev(black_box(x)));
            }
        })
    });
    
    group.bench_function("v5_hybrid_polynomial", |b| {
        b.iter(|| {
            for &x in &test_values {
                black_box(fast_inv_sqrt_v5_hybrid_polynomial(black_box(x)));
            }
        })
    });
    
    group.finish();
    
    // Range-specific benchmarks
    let small_values: Vec<f32> = (1..=100).map(|i| i as f32 * 1e-6).collect();
    let normal_values: Vec<f32> = (1..=100).map(|i| i as f32 * 0.1).collect();
    let large_values: Vec<f32> = (1..=100).map(|i| i as f32 * 100.0).collect();
    
    let ranges = [
        ("small_values", &small_values),
        ("normal_values", &normal_values),
        ("large_values", &large_values),
    ];
    
    for (range_name, values) in &ranges {
        let mut group = c.benchmark_group(format!("fast_inv_sqrt_{}", range_name));
        
        group.bench_function("std_library", |b| {
            b.iter(|| {
                for &x in *values {
                    black_box(std_inv_sqrt(black_box(x)));
                }
            })
        });
        
        group.bench_function("v1_quake_optimized", |b| {
            b.iter(|| {
                for &x in *values {
                    black_box(fast_inv_sqrt_v1_quake_optimized(black_box(x)));
                }
            })
        });
        
        group.bench_function("v2_improved_magic", |b| {
            b.iter(|| {
                for &x in *values {
                    black_box(fast_inv_sqrt_v2_improved_magic(black_box(x)));
                }
            })
        });
        
        group.finish();
    }
}

criterion_group!(benches, benchmark_fast_inv_sqrt_implementations);
criterion_main!(benches);
